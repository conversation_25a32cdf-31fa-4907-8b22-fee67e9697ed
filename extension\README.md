# GhostLayer Chrome Extension

Transform AI-generated text into natural, human-like writing instantly on any website!

## 🚀 Features

### ⚡ Quick Actions
- **Right-click Context Menu**: Humanize selected text with different styles
- **Keyboard Shortcuts**: 
  - `Ctrl+Shift+H` (or `Cmd+Shift+H` on Mac) - Humanize selected text
  - `Ctrl+Shift+G` (or `Cmd+Shift+G` on Mac) - Toggle floating widget
- **Floating Widget**: Always-accessible humanization tool
- **Popup Interface**: Full-featured popup with manual input and settings

### 🎨 Writing Styles
- **📚 Academic**: Formal, scholarly tone
- **💼 Professional**: Business-appropriate language
- **🎨 Creative**: Engaging, expressive writing
- **⚙️ Technical**: Clear, precise technical communication

### 🌍 Multi-Language Support
- **English**: Full feature support
- **Vietnamese**: Native language processing
- **Chinese**: Simplified and Traditional variants
- **Auto-Detection**: Automatically detects text language

### 📊 Analytics & Stats
- **Usage Tracking**: Daily and total transformation counts
- **Performance Metrics**: Processing time and improvement scores
- **Website Analytics**: See which sites you use <PERSON><PERSON>ayer on most

### ⚙️ Customization
- **Auto-detect Language**: Automatically identify text language
- **Notifications**: Toggle success/error notifications
- **Usage Analytics**: Control data collection preferences

## 🔧 Installation

### From Chrome Web Store (Coming Soon)
1. Visit the Chrome Web Store
2. Search for "GhostLayer"
3. Click "Add to Chrome"
4. Follow the installation prompts

### Manual Installation (Developer Mode)
1. Download or clone this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the `extension` folder
5. The GhostLayer extension should now appear in your extensions

## 📖 How to Use

### Method 1: Right-Click Context Menu
1. Select any text on a webpage
2. Right-click and choose "🤖➡️👤 Humanize with GhostLayer"
3. Select your preferred writing style
4. View the humanized result in a modal

### Method 2: Floating Widget
1. Press `Ctrl+Shift+G` to show the floating widget
2. Select text and click "Humanize Selection"
3. Or use "Paste & Humanize" for clipboard content

### Method 3: Extension Popup
1. Click the GhostLayer icon in your browser toolbar
2. Paste text into the input area
3. Choose your writing style
4. Click "Humanize Text"

### Method 4: Keyboard Shortcuts
1. Select text on any webpage
2. Press `Ctrl+Shift+H` to humanize with default style
3. View results in the modal popup

## 🎯 Use Cases

### Content Creation
- **Blog Posts**: Transform AI drafts into engaging content
- **Social Media**: Make AI captions more natural
- **Marketing Copy**: Humanize promotional content

### Academic & Professional
- **Research Papers**: Improve AI-generated academic text
- **Business Documents**: Enhance professional communications
- **Technical Documentation**: Clarify AI-written instructions

### Personal Use
- **Emails**: Make AI-assisted emails more personal
- **Creative Writing**: Enhance AI story drafts
- **Learning**: Improve AI-generated study materials

## 🔒 Privacy & Security

### Local Processing
- All text processing happens locally in your browser
- No text data is sent to external servers
- Your content remains completely private

### Optional Analytics
- Usage statistics are collected only if enabled
- No personal information or text content is tracked
- Analytics help improve the extension's performance

### Permissions Explained
- **activeTab**: Access current tab to read selected text
- **contextMenus**: Add right-click menu options
- **storage**: Save your preferences and usage stats
- **scripting**: Inject the floating widget into web pages

## ⚙️ Settings

### Language Settings
- **Auto-detect Language**: Automatically identify text language
- **Default Style**: Set your preferred writing style
- **Default Intensity**: Choose processing intensity level

### Interface Settings
- **Show Notifications**: Toggle success/error messages
- **Widget Position**: Customize floating widget placement
- **Keyboard Shortcuts**: Modify hotkey combinations

### Privacy Settings
- **Usage Analytics**: Control data collection
- **Website Tracking**: Track which sites you use GhostLayer on
- **Performance Metrics**: Collect processing time data

## 🚀 Viral Features

### Social Sharing
- **Before/After Comparisons**: Visual transformation showcases
- **Improvement Metrics**: Share your humanization scores
- **Social Media Integration**: One-click sharing to platforms
- **Viral Challenges**: Participate in community competitions

### Community Integration
- **Transformation Gallery**: Share your best results
- **Achievement System**: Unlock badges and rewards
- **Leaderboards**: Compete with other users
- **Usage Statistics**: Track your improvement over time

## 🛠️ Technical Details

### Architecture
- **Manifest V3**: Latest Chrome extension standard
- **Content Scripts**: Inject UI into web pages
- **Background Service Worker**: Handle processing and storage
- **Popup Interface**: Full-featured extension popup

### Performance
- **Fast Processing**: Optimized algorithms for quick results
- **Memory Efficient**: Minimal resource usage
- **Offline Capable**: Works without internet connection
- **Cross-Platform**: Compatible with all Chromium browsers

### Compatibility
- **Chrome**: Version 88+
- **Edge**: Version 88+
- **Brave**: Latest version
- **Opera**: Version 74+

## 🐛 Troubleshooting

### Common Issues

**Extension not working on some websites**
- Some sites block content scripts for security
- Try refreshing the page after installing
- Check if the site uses a strict Content Security Policy

**Keyboard shortcuts not working**
- Check if another extension is using the same shortcuts
- Verify shortcuts in `chrome://extensions/shortcuts`
- Try using the right-click menu instead

**Text not being replaced**
- The extension can only replace text in editable elements
- Use copy/paste for non-editable content
- Some rich text editors may not support replacement

**Performance issues**
- Large text blocks may take longer to process
- Try breaking text into smaller chunks
- Check if other extensions are conflicting

### Getting Help
- Visit our [Help Center](https://ghostlayer.app/help)
- Submit feedback through the extension popup
- Report bugs on our [GitHub repository](https://github.com/ghostlayer/extension)

## 🔄 Updates

### Version 1.0.0 (Current)
- Initial release with core humanization features
- Multi-language support (English, Vietnamese, Chinese)
- Floating widget and popup interface
- Social sharing and community features

### Upcoming Features
- **API Integration**: Connect with popular writing tools
- **Batch Processing**: Humanize multiple texts at once
- **Custom Styles**: Create your own writing styles
- **Team Collaboration**: Share settings across team members

## 📄 License

This extension is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with love by the GhostLayer team
- Icons provided by [Lucide](https://lucide.dev/)
- UI components inspired by [Tailwind CSS](https://tailwindcss.com/)

---

**Transform your AI text instantly with GhostLayer! 🤖➡️👤**
