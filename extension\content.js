// Content script for GhostLayer Chrome Extension
// Handles UI injection, text selection, and result display on web pages

class GhostLayerWidget {
  constructor() {
    this.widget = null;
    this.isVisible = false;
    this.resultModal = null;
    this.init();
  }

  init() {
    this.createWidget();
    this.setupEventListeners();
    this.injectStyles();
  }

  createWidget() {
    // Create floating widget
    this.widget = document.createElement('div');
    this.widget.id = 'ghostlayer-widget';
    this.widget.innerHTML = `
      <div class="gl-widget-container">
        <div class="gl-widget-header">
          <div class="gl-logo">👻</div>
          <span class="gl-title">GhostLayer</span>
          <button class="gl-close" id="gl-close-widget">×</button>
        </div>
        <div class="gl-widget-content">
          <div class="gl-quick-actions">
            <button class="gl-btn gl-btn-primary" id="gl-humanize-selection">
              🤖➡️👤 Humanize Selection
            </button>
            <button class="gl-btn gl-btn-secondary" id="gl-paste-and-humanize">
              📋 Paste & Humanize
            </button>
          </div>
          <div class="gl-style-selector">
            <label>Style:</label>
            <select id="gl-style-select">
              <option value="academic">📚 Academic</option>
              <option value="professional">💼 Professional</option>
              <option value="creative">🎨 Creative</option>
              <option value="technical">⚙️ Technical</option>
            </select>
          </div>
          <div class="gl-stats">
            <div class="gl-stat">
              <span class="gl-stat-label">Today:</span>
              <span class="gl-stat-value" id="gl-today-count">0</span>
            </div>
            <div class="gl-stat">
              <span class="gl-stat-label">Total:</span>
              <span class="gl-stat-value" id="gl-total-count">0</span>
            </div>
          </div>
        </div>
      </div>
    `;

    // Position widget
    this.widget.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      transform: translateX(100%);
      transition: transform 0.3s ease;
    `;

    document.body.appendChild(this.widget);
    this.updateStats();
  }

  setupEventListeners() {
    // Widget toggle
    document.getElementById('gl-close-widget').addEventListener('click', () => {
      this.hideWidget();
    });

    // Humanize selection button
    document.getElementById('gl-humanize-selection').addEventListener('click', () => {
      const selectedText = window.getSelection().toString().trim();
      if (selectedText) {
        const style = document.getElementById('gl-style-select').value;
        this.humanizeText(selectedText, style);
      } else {
        this.showNotification('Please select some text first', 'warning');
      }
    });

    // Paste and humanize button
    document.getElementById('gl-paste-and-humanize').addEventListener('click', async () => {
      try {
        const text = await navigator.clipboard.readText();
        if (text.trim()) {
          const style = document.getElementById('gl-style-select').value;
          this.humanizeText(text, style);
        } else {
          this.showNotification('Clipboard is empty', 'warning');
        }
      } catch (error) {
        this.showNotification('Could not access clipboard', 'error');
      }
    });

    // Double-click to show widget
    document.addEventListener('dblclick', (e) => {
      if (e.ctrlKey || e.metaKey) {
        this.toggleWidget();
      }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + Shift + H to humanize selection
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'H') {
        e.preventDefault();
        const selectedText = window.getSelection().toString().trim();
        if (selectedText) {
          this.humanizeText(selectedText, 'academic');
        }
      }
      
      // Ctrl/Cmd + Shift + G to toggle widget
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'G') {
        e.preventDefault();
        this.toggleWidget();
      }
    });
  }

  injectStyles() {
    const style = document.createElement('style');
    style.textContent = `
      #ghostlayer-widget {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.4;
      }

      .gl-widget-container {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        width: 280px;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
      }

      .gl-widget-header {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px 12px 0 0;
      }

      .gl-logo {
        font-size: 18px;
        margin-right: 8px;
      }

      .gl-title {
        font-weight: 600;
        flex: 1;
        background: linear-gradient(45deg, #60a5fa, #a855f7);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .gl-close {
        background: none;
        border: none;
        color: #94a3b8;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: all 0.2s;
      }

      .gl-close:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
      }

      .gl-widget-content {
        padding: 16px;
      }

      .gl-quick-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 16px;
      }

      .gl-btn {
        padding: 10px 12px;
        border: none;
        border-radius: 8px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
      }

      .gl-btn-primary {
        background: linear-gradient(45deg, #3b82f6, #8b5cf6);
        color: white;
      }

      .gl-btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
      }

      .gl-btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        color: #e2e8f0;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .gl-btn-secondary:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .gl-style-selector {
        margin-bottom: 16px;
      }

      .gl-style-selector label {
        display: block;
        margin-bottom: 6px;
        font-size: 12px;
        color: #94a3b8;
        font-weight: 500;
      }

      .gl-style-selector select {
        width: 100%;
        padding: 8px 12px;
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        color: white;
        font-size: 13px;
      }

      .gl-stats {
        display: flex;
        justify-content: space-between;
        padding-top: 12px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
      }

      .gl-stat {
        text-align: center;
      }

      .gl-stat-label {
        display: block;
        font-size: 11px;
        color: #94a3b8;
        margin-bottom: 2px;
      }

      .gl-stat-value {
        font-size: 16px;
        font-weight: 600;
        color: #60a5fa;
      }

      /* Result Modal */
      .gl-result-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        z-index: 10001;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .gl-result-content {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        border-radius: 16px;
        max-width: 600px;
        width: 100%;
        max-height: 80vh;
        overflow-y: auto;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .gl-result-header {
        padding: 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .gl-result-title {
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .gl-result-body {
        padding: 20px;
      }

      .gl-comparison {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 20px;
      }

      .gl-text-box {
        padding: 16px;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .gl-text-box.original {
        background: rgba(239, 68, 68, 0.1);
        border-color: rgba(239, 68, 68, 0.3);
      }

      .gl-text-box.humanized {
        background: rgba(34, 197, 94, 0.1);
        border-color: rgba(34, 197, 94, 0.3);
      }

      .gl-text-label {
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .gl-text-label.original {
        color: #fca5a5;
      }

      .gl-text-label.humanized {
        color: #86efac;
      }

      .gl-text-content {
        font-size: 14px;
        line-height: 1.5;
        color: #e2e8f0;
      }

      .gl-result-actions {
        display: flex;
        gap: 12px;
        justify-content: center;
        padding-top: 16px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
      }

      /* Notification */
      .gl-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10002;
        padding: 12px 16px;
        border-radius: 8px;
        color: white;
        font-size: 14px;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease;
      }

      .gl-notification.show {
        transform: translateX(0);
      }

      .gl-notification.success {
        background: linear-gradient(45deg, #10b981, #059669);
      }

      .gl-notification.error {
        background: linear-gradient(45deg, #ef4444, #dc2626);
      }

      .gl-notification.warning {
        background: linear-gradient(45deg, #f59e0b, #d97706);
      }

      .gl-notification.info {
        background: linear-gradient(45deg, #3b82f6, #2563eb);
      }
    `;
    document.head.appendChild(style);
  }

  showWidget() {
    this.isVisible = true;
    this.widget.style.transform = 'translateX(0)';
  }

  hideWidget() {
    this.isVisible = false;
    this.widget.style.transform = 'translateX(100%)';
  }

  toggleWidget() {
    if (this.isVisible) {
      this.hideWidget();
    } else {
      this.showWidget();
    }
  }

  humanizeText(text, style) {
    chrome.runtime.sendMessage({
      action: 'humanizeText',
      text: text,
      style: style
    });
  }

  showResult(data) {
    this.createResultModal(data);
    this.updateStats();
  }

  createResultModal(data) {
    // Remove existing modal
    if (this.resultModal) {
      this.resultModal.remove();
    }

    this.resultModal = document.createElement('div');
    this.resultModal.className = 'gl-result-modal';
    this.resultModal.innerHTML = `
      <div class="gl-result-content">
        <div class="gl-result-header">
          <div class="gl-result-title">
            ✨ Text Humanized Successfully!
          </div>
          <button class="gl-close" id="gl-close-result">×</button>
        </div>
        <div class="gl-result-body">
          <div class="gl-comparison">
            <div class="gl-text-box original">
              <div class="gl-text-label original">Original (AI-Generated)</div>
              <div class="gl-text-content">${data.originalText}</div>
            </div>
            <div class="gl-text-box humanized">
              <div class="gl-text-label humanized">Humanized (${data.improvementScore}% Better)</div>
              <div class="gl-text-content">${data.humanizedText}</div>
            </div>
          </div>
          <div class="gl-result-actions">
            <button class="gl-btn gl-btn-primary" id="gl-copy-result">
              📋 Copy Humanized Text
            </button>
            <button class="gl-btn gl-btn-secondary" id="gl-share-result">
              📤 Share Transformation
            </button>
            <button class="gl-btn gl-btn-secondary" id="gl-replace-original">
              🔄 Replace Original
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(this.resultModal);

    // Event listeners
    document.getElementById('gl-close-result').addEventListener('click', () => {
      this.resultModal.remove();
    });

    document.getElementById('gl-copy-result').addEventListener('click', () => {
      navigator.clipboard.writeText(data.humanizedText);
      this.showNotification('Copied to clipboard!', 'success');
    });

    document.getElementById('gl-share-result').addEventListener('click', () => {
      const shareText = `🤖➡️👤 Just improved my AI text by ${data.improvementScore}% with @GhostLayer!\n\nBefore: "${data.originalText.substring(0, 100)}..."\nAfter: "${data.humanizedText.substring(0, 100)}..."\n\nTry it free: https://ghostlayer.app`;
      navigator.clipboard.writeText(shareText);
      this.showNotification('Share text copied to clipboard!', 'success');
    });

    document.getElementById('gl-replace-original').addEventListener('click', () => {
      // Try to replace the original text if it's in an editable element
      this.replaceSelectedText(data.humanizedText);
      this.resultModal.remove();
    });

    // Close on outside click
    this.resultModal.addEventListener('click', (e) => {
      if (e.target === this.resultModal) {
        this.resultModal.remove();
      }
    });
  }

  replaceSelectedText(newText) {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const selectedElement = range.commonAncestorContainer;
      
      // Check if the selected text is in an editable element
      const editableElement = this.findEditableParent(selectedElement);
      if (editableElement) {
        range.deleteContents();
        range.insertNode(document.createTextNode(newText));
        this.showNotification('Text replaced successfully!', 'success');
      } else {
        this.showNotification('Cannot replace text in this element', 'warning');
      }
    }
  }

  findEditableParent(element) {
    let current = element;
    while (current && current !== document.body) {
      if (current.nodeType === Node.ELEMENT_NODE) {
        if (current.isContentEditable || 
            current.tagName === 'TEXTAREA' || 
            current.tagName === 'INPUT') {
          return current;
        }
      }
      current = current.parentNode;
    }
    return null;
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `gl-notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
      notification.classList.add('show');
    }, 100);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, 3000);
  }

  async updateStats() {
    try {
      const today = new Date().toDateString();
      const stats = await chrome.storage.local.get(['dailyStats', 'totalStats']);
      
      const dailyStats = stats.dailyStats || {};
      const todayCount = dailyStats[today] || 0;
      const totalCount = stats.totalStats || 0;
      
      document.getElementById('gl-today-count').textContent = todayCount;
      document.getElementById('gl-total-count').textContent = totalCount;
    } catch (error) {
      console.error('Failed to update stats:', error);
    }
  }
}

// Initialize widget when page loads
let ghostLayerWidget;

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    ghostLayerWidget = new GhostLayerWidget();
  });
} else {
  ghostLayerWidget = new GhostLayerWidget();
}

// Listen for messages from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'getSelectedText':
      const selectedText = window.getSelection().toString().trim();
      sendResponse({ selectedText: selectedText });
      break;
      
    case 'toggleWidget':
      if (ghostLayerWidget) {
        ghostLayerWidget.toggleWidget();
      }
      break;
      
    case 'showResult':
      if (ghostLayerWidget) {
        ghostLayerWidget.showResult(request.data);
      }
      break;
      
    case 'showNotification':
      if (ghostLayerWidget) {
        ghostLayerWidget.showNotification(request.message, request.type);
      }
      break;
  }
});
