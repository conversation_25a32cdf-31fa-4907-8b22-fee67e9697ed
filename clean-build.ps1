# Clean build script for Windows
Write-Host "Stopping all Node.js processes..."
taskkill /f /im node.exe 2>$null

Write-Host "Waiting for processes to stop..."
Start-Sleep -Seconds 2

Write-Host "Taking ownership of .next directory..."
if (Test-Path ".next") {
    takeown /f .next /r /d y 2>$null
    icacls .next /grant administrators:F /t 2>$null
    Remove-Item -Path .next -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host "Taking ownership of build directory..."
if (Test-Path "build") {
    takeown /f build /r /d y 2>$null
    icacls build /grant administrators:F /t 2>$null
    Remove-Item -Path build -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host "Cleaning npm cache..."
npm cache clean --force

Write-Host "Installing dependencies..."
npm install

Write-Host "Starting build..."
npm run build
