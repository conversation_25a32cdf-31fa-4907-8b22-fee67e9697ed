// Service Worker for GhostLayer PWA
// Handles offline caching, push notifications, and background sync

const CACHE_NAME = 'ghostlayer-v1.0.0';
const STATIC_CACHE = 'ghostlayer-static-v1.0.0';
const DYNAMIC_CACHE = 'ghostlayer-dynamic-v1.0.0';

// Files to cache for offline functionality
const STATIC_FILES = [
  '/',
  '/manifest.json',
  '/icon-192x192.png',
  '/icon-512x512.png',
  '/apple-touch-icon.png',
  '/_next/static/css/app/layout.css',
  '/_next/static/css/app/globals.css',
  '/offline.html'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /^https:\/\/api\.ghostlayer\.app\//,
  /^https:\/\/ghostlayer\.app\/api\//
];

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static files
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('Service Worker: Caching static files');
        return cache.addAll(STATIC_FILES);
      }),
      
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== CACHE_NAME) {
              console.log('Service Worker: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      
      // Take control of all clients
      self.clients.claim()
    ])
  );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Handle different types of requests
  if (url.pathname.startsWith('/_next/static/')) {
    // Static assets - cache first
    event.respondWith(cacheFirst(request, STATIC_CACHE));
  } else if (API_CACHE_PATTERNS.some(pattern => pattern.test(request.url))) {
    // API requests - network first with cache fallback
    event.respondWith(networkFirst(request, DYNAMIC_CACHE));
  } else if (url.pathname === '/' || url.pathname.startsWith('/app/')) {
    // App pages - network first with cache fallback
    event.respondWith(networkFirst(request, DYNAMIC_CACHE));
  } else {
    // Other requests - network first
    event.respondWith(
      fetch(request).catch(() => {
        // Fallback to offline page for navigation requests
        if (request.mode === 'navigate') {
          return caches.match('/offline.html');
        }
      })
    );
  }
});

// Cache first strategy
async function cacheFirst(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      // Update cache in background
      fetch(request).then((response) => {
        if (response.ok) {
          cache.put(request, response.clone());
        }
      }).catch(() => {
        // Ignore network errors
      });
      
      return cachedResponse;
    }
    
    // Not in cache, fetch from network
    const response = await fetch(request);
    if (response.ok) {
      cache.put(request, response.clone());
    }
    return response;
    
  } catch (error) {
    console.error('Cache first strategy failed:', error);
    throw error;
  }
}

// Network first strategy
async function networkFirst(request, cacheName) {
  try {
    const response = await fetch(request);
    
    if (response.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, response.clone());
    }
    
    return response;
    
  } catch (error) {
    console.log('Network failed, trying cache:', request.url);
    
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // If it's a navigation request, return offline page
    if (request.mode === 'navigate') {
      return caches.match('/offline.html');
    }
    
    throw error;
  }
}

// Push notification event
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received');
  
  let notificationData = {
    title: 'GhostLayer',
    body: 'You have a new notification',
    icon: '/icon-192x192.png',
    badge: '/icon-192x192.png',
    tag: 'ghostlayer-notification',
    requireInteraction: false,
    actions: [
      {
        action: 'open',
        title: 'Open App',
        icon: '/icon-192x192.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ]
  };
  
  if (event.data) {
    try {
      const data = event.data.json();
      notificationData = { ...notificationData, ...data };
    } catch (error) {
      console.error('Error parsing push data:', error);
    }
  }
  
  event.waitUntil(
    self.registration.showNotification(notificationData.title, notificationData)
  );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked');
  
  event.notification.close();
  
  if (event.action === 'open' || !event.action) {
    event.waitUntil(
      clients.matchAll({ type: 'window' }).then((clientList) => {
        // Check if app is already open
        for (const client of clientList) {
          if (client.url.includes('ghostlayer') && 'focus' in client) {
            return client.focus();
          }
        }
        
        // Open new window
        if (clients.openWindow) {
          return clients.openWindow('/');
        }
      })
    );
  }
});

// Background sync event
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered');
  
  if (event.tag === 'background-humanize') {
    event.waitUntil(processBackgroundHumanization());
  } else if (event.tag === 'analytics-sync') {
    event.waitUntil(syncAnalytics());
  }
});

// Process queued humanization requests
async function processBackgroundHumanization() {
  try {
    // Get queued requests from IndexedDB
    const queuedRequests = await getQueuedRequests();
    
    for (const request of queuedRequests) {
      try {
        // Process the text
        const result = await processTextOffline(request.text, request.options);
        
        // Store result
        await storeProcessedResult(request.id, result);
        
        // Remove from queue
        await removeFromQueue(request.id);
        
        // Notify user if app is open
        const clients = await self.clients.matchAll();
        clients.forEach(client => {
          client.postMessage({
            type: 'BACKGROUND_PROCESSING_COMPLETE',
            requestId: request.id,
            result: result
          });
        });
        
      } catch (error) {
        console.error('Background processing failed for request:', request.id, error);
      }
    }
    
  } catch (error) {
    console.error('Background humanization failed:', error);
  }
}

// Sync analytics data
async function syncAnalytics() {
  try {
    const analyticsData = await getPendingAnalytics();
    
    if (analyticsData.length > 0) {
      const response = await fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ events: analyticsData })
      });
      
      if (response.ok) {
        await clearPendingAnalytics();
        console.log('Analytics synced successfully');
      }
    }
    
  } catch (error) {
    console.error('Analytics sync failed:', error);
  }
}

// Offline text processing (simplified version)
async function processTextOffline(text, options) {
  // This would use the same processing engine as the main app
  // For now, return a mock result
  return {
    humanizedText: `[Processed Offline] ${text}`,
    improvementScore: Math.floor(70 + Math.random() * 25),
    processingTime: 1000 + Math.random() * 2000,
    variations: []
  };
}

// IndexedDB helper functions (simplified)
async function getQueuedRequests() {
  // In a real implementation, this would use IndexedDB
  return [];
}

async function storeProcessedResult(id, result) {
  // Store in IndexedDB
  console.log('Storing result for request:', id);
}

async function removeFromQueue(id) {
  // Remove from IndexedDB queue
  console.log('Removing request from queue:', id);
}

async function getPendingAnalytics() {
  // Get pending analytics from IndexedDB
  return [];
}

async function clearPendingAnalytics() {
  // Clear analytics from IndexedDB
  console.log('Analytics cleared');
}

// Message event - handle messages from main app
self.addEventListener('message', (event) => {
  console.log('Service Worker: Message received:', event.data);
  
  switch (event.data.type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'QUEUE_HUMANIZATION':
      // Queue text for background processing
      queueHumanizationRequest(event.data.payload);
      break;
      
    case 'CACHE_ANALYTICS':
      // Cache analytics data for later sync
      cacheAnalyticsData(event.data.payload);
      break;
      
    default:
      console.log('Unknown message type:', event.data.type);
  }
});

async function queueHumanizationRequest(payload) {
  // Add to IndexedDB queue
  console.log('Queuing humanization request:', payload);
  
  // Register background sync
  try {
    await self.registration.sync.register('background-humanize');
  } catch (error) {
    console.error('Background sync registration failed:', error);
  }
}

async function cacheAnalyticsData(payload) {
  // Cache analytics data
  console.log('Caching analytics data:', payload);
  
  // Register analytics sync
  try {
    await self.registration.sync.register('analytics-sync');
  } catch (error) {
    console.error('Analytics sync registration failed:', error);
  }
}
