'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { Upload, FileText, Wand2, AlertTriangle } from 'lucide-react';
import { analytics } from '@/lib/analytics';
import { validateTextContent, sanitizeInput, checkRateLimit } from '@/lib/security';

interface TextInputProps {
  value: string;
  onChange: (value: string) => void;
  onProcess: () => void;
  isProcessing: boolean;
}

export default function TextInput({ value, onChange, onProcess, isProcessing }: TextInputProps) {
  const [dragOver, setDragOver] = useState(false);
  const [securityError, setSecurityError] = useState<string | null>(null);

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const textFile = files.find(file => file.type === 'text/plain');

    if (textFile) {
      // Security: Check file size (max 10MB)
      if (textFile.size > 10 * 1024 * 1024) {
        setSecurityError('File size exceeds 10MB limit');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;

        // Security: Validate and sanitize content (client-side only)
        if (typeof window !== 'undefined') {
          try {
            const validation = validateTextContent(content);
            if (!validation.isValid) {
              setSecurityError(validation.error || 'Invalid file content');
              return;
            }

            const sanitizedContent = sanitizeInput(content);
            onChange(sanitizedContent);
            setSecurityError(null);
          } catch (error) {
            console.warn('Security validation failed:', error);
            onChange(content);
            setSecurityError(null);
          }
        } else {
          onChange(content);
          setSecurityError(null);
        }
        setSecurityError(null);

        // Track analytics
        analytics.trackFileUpload(textFile.type, textFile.size);
      };
      reader.readAsText(textFile);
    }
  };

  const handleTextChange = (newValue: string) => {
    // Only run security validation on client-side
    if (typeof window !== 'undefined') {
      try {
        // Security: Validate input
        const validation = validateTextContent(newValue);
        if (!validation.isValid) {
          setSecurityError(validation.error || 'Invalid input');
          return;
        }

        // Security: Sanitize input
        const sanitizedValue = sanitizeInput(newValue);
        onChange(sanitizedValue);
        setSecurityError(null);
      } catch (error) {
        // Fallback if security validation fails
        console.warn('Security validation failed:', error);
        onChange(newValue);
        setSecurityError(null);
      }
    } else {
      // Server-side rendering fallback
      onChange(newValue);
      setSecurityError(null);
    }
  };

  const handleProcess = () => {
    // Only run security validation on client-side
    if (typeof window !== 'undefined') {
      try {
        // Security: Rate limiting check
        if (!checkRateLimit('text_processing')) {
          setSecurityError('Too many requests. Please wait before processing again.');
          return;
        }

        // Security: Final validation before processing
        const validation = validateTextContent(value);
        if (!validation.isValid) {
          setSecurityError(validation.error || 'Invalid input for processing');
          return;
        }

        setSecurityError(null);
        onProcess();
      } catch (error) {
        // Fallback if security validation fails
        console.warn('Security validation failed:', error);
        setSecurityError(null);
        onProcess();
      }
    } else {
      // Server-side rendering fallback
      setSecurityError(null);
      onProcess();
    }
  };

  const wordCount = value.trim().split(/\s+/).filter(word => word.length > 0).length;
  const charCount = value.length;

  return (
    <Card className="p-6 bg-white/5 backdrop-blur-lg border-white/10">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-white flex items-center gap-2">
          <FileText className="w-5 h-5" />
          Input Text
        </h2>
        
        <div className="text-sm text-gray-400">
          {wordCount} words · {charCount} characters
        </div>
      </div>
      
      <div
        className={`relative transition-all duration-200 ${
          dragOver ? 'ring-2 ring-blue-400 ring-opacity-50' : ''
        }`}
        onDragOver={(e) => {
          e.preventDefault();
          setDragOver(true);
        }}
        onDragLeave={() => setDragOver(false)}
        onDrop={handleDrop}
      >
        <Textarea
          value={value}
          onChange={(e) => handleTextChange(e.target.value)}
          placeholder="Paste your AI-generated text here, or drag and drop a text file..."
          className="min-h-[200px] bg-slate-800/50 border-slate-700 text-white placeholder-gray-400 resize-none focus:ring-blue-500 focus:border-blue-500"
        />
        
        {dragOver && (
          <div className="absolute inset-0 bg-blue-500/10 border-2 border-dashed border-blue-400 rounded-md flex items-center justify-center">
            <div className="text-blue-400 text-center">
              <Upload className="w-8 h-8 mx-auto mb-2" />
              <p>Drop your text file here</p>
            </div>
          </div>
        )}
      </div>

      {securityError && (
        <div className="mt-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg flex items-center gap-2 text-red-400">
          <AlertTriangle className="w-4 h-4 flex-shrink-0" />
          <span className="text-sm">{securityError}</span>
        </div>
      )}

      <div className="flex justify-between items-center mt-4">
        <div className="text-xs text-gray-500">
          Supports plain text files up to 10MB
        </div>
        
        <Button
          onClick={handleProcess}
          disabled={!value.trim() || isProcessing || !!securityError}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6"
        >
          {isProcessing ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Processing...
            </>
          ) : (
            <>
              <Wand2 className="w-4 h-4 mr-2" />
              Humanize Text
            </>
          )}
        </Button>
      </div>
    </Card>
  );
}