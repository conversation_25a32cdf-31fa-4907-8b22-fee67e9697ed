'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Brain, Shield, Zap, LogIn } from 'lucide-react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import AuthModal from './AuthModal';
import UserProfileDropdown from './UserProfileDropdown';

export default function Header() {
  const { data: session } = useSession();
  const [showAuthModal, setShowAuthModal] = useState(false);

  return (
    <>
      <header className="relative">
        {/* Navigation Bar */}
        <nav className="flex items-center justify-between p-4 mb-8">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="absolute inset-0 bg-blue-500/20 blur-xl rounded-full"></div>
              <Image
                src="/logo.jpg"
                alt="GhostLayer Logo"
                width={48}
                height={48}
                className="w-12 h-12 rounded-xl object-cover relative z-10"
              />
            </div>
            <h1 className="text-2xl font-bold text-white bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              GhostLayer
            </h1>
          </div>

          <div className="flex items-center gap-4">
            {session ? (
              <UserProfileDropdown />
            ) : (
              <Button
                onClick={() => setShowAuthModal(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <LogIn className="w-4 h-4 mr-2" />
                Sign In
              </Button>
            )}
          </div>
        </nav>

        {/* Main Header Content */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="absolute inset-0 bg-blue-500/20 blur-xl rounded-full"></div>
              <Image
                src="/logo.jpg"
                alt="GhostLayer Logo"
                width={96}
                height={96}
                className="w-24 h-24 rounded-2xl object-cover relative z-10"
              />
            </div>
          </div>

          <h1 className="text-5xl font-bold text-white mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            GhostLayer
          </h1>

          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Transform AI-generated content into natural, human-like writing while preserving meaning and intent.
          </p>

          <div className="flex flex-wrap justify-center gap-6 text-gray-400">
            <div className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-green-400" />
              <span>Undetectable Content</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-yellow-400" />
              <span>Lightning Fast</span>
            </div>
            <div className="flex items-center gap-2">
              <Brain className="w-5 h-5 text-blue-400" />
              <span>AI-Powered</span>
            </div>
          </div>
        </div>
      </header>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </>
  );
}