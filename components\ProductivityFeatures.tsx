'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Zap, 
  Upload, 
  Download, 
  Code, 
  Settings, 
  Workflow,
  FileText,
  Database,
  Link,
  CheckCircle,
  Clock,
  BarChart3
} from 'lucide-react';
import { analytics } from '@/lib/analytics';

interface ProductivityFeaturesProps {
  onFeatureUse: (feature: string, data: any) => void;
}

export default function ProductivityFeatures({ onFeatureUse }: ProductivityFeaturesProps) {
  const [activeTab, setActiveTab] = useState('batch');
  const [batchFiles, setBatchFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [api<PERSON><PERSON>, setApi<PERSON>ey] = useState('');

  const handleBatchUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setBatchFiles(files);
    analytics.trackShare('batch_upload');
  };

  const processBatchFiles = async () => {
    if (batchFiles.length === 0) return;
    
    setIsProcessing(true);
    try {
      // Simulate batch processing
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      onFeatureUse('batch_processing', {
        filesProcessed: batchFiles.length,
        totalTime: 3000,
        avgImprovementScore: 87
      });
      
      analytics.trackShare('batch_processing_completed');
    } catch (error) {
      console.error('Batch processing failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const generateApiKey = () => {
    const newApiKey = 'gl_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    setApiKey(newApiKey);
    analytics.trackShare('api_key_generated');
  };

  return (
    <Card className="bg-white/5 backdrop-blur-lg border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-white">
          <div className="p-2 bg-green-500/20 rounded-lg">
            <Zap className="w-5 h-5 text-green-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Productivity & Enterprise</h3>
            <p className="text-gray-400 text-sm font-normal">Advanced tools for power users and teams</p>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-slate-800/50">
            <TabsTrigger value="batch" className="text-white">📁 Batch</TabsTrigger>
            <TabsTrigger value="api" className="text-white">🔌 API</TabsTrigger>
            <TabsTrigger value="integrations" className="text-white">🔗 Integrations</TabsTrigger>
          </TabsList>

          {/* Batch Processing */}
          <TabsContent value="batch" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-white font-semibold">Batch Processing</h4>
                <Badge className="bg-green-500/20 text-green-400">
                  Enterprise Feature
                </Badge>
              </div>
              
              <div className="p-4 bg-white/5 rounded-lg border-2 border-dashed border-gray-600">
                <div className="text-center">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-300 mb-3">Upload multiple files for batch humanization</p>
                  <input
                    type="file"
                    multiple
                    accept=".txt,.md,.docx"
                    onChange={handleBatchUpload}
                    className="hidden"
                    id="batch-upload"
                  />
                  <label htmlFor="batch-upload">
                    <Button className="bg-blue-600 hover:bg-blue-700 cursor-pointer">
                      <Upload className="w-4 h-4 mr-2" />
                      Select Files
                    </Button>
                  </label>
                </div>
              </div>

              {batchFiles.length > 0 && (
                <div className="space-y-3">
                  <h5 className="text-white font-medium">Selected Files ({batchFiles.length})</h5>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {batchFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-white/5 rounded">
                        <div className="flex items-center gap-2">
                          <FileText className="w-4 h-4 text-blue-400" />
                          <span className="text-gray-300 text-sm">{file.name}</span>
                        </div>
                        <span className="text-gray-400 text-xs">
                          {(file.size / 1024).toFixed(1)} KB
                        </span>
                      </div>
                    ))}
                  </div>
                  
                  <Button 
                    onClick={processBatchFiles}
                    disabled={isProcessing}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Processing {batchFiles.length} files...
                      </>
                    ) : (
                      <>
                        <Zap className="w-4 h-4 mr-2" />
                        Process All Files
                      </>
                    )}
                  </Button>
                </div>
              )}

              <div className="grid grid-cols-3 gap-4">
                <div className="p-3 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-400">50+</div>
                  <div className="text-gray-400 text-sm">File Formats</div>
                </div>
                <div className="p-3 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-400">10GB</div>
                  <div className="text-gray-400 text-sm">Max Size</div>
                </div>
                <div className="p-3 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-purple-400">24/7</div>
                  <div className="text-gray-400 text-sm">Processing</div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* API Access */}
          <TabsContent value="api" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-white font-semibold">API Access</h4>
                <Badge className="bg-purple-500/20 text-purple-400">
                  Developer Tools
                </Badge>
              </div>

              <div className="p-4 bg-white/5 rounded-lg">
                <h5 className="text-white font-medium mb-3">API Key</h5>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={apiKey}
                    placeholder="Generate an API key to get started"
                    readOnly
                    className="flex-1 p-2 bg-black/30 border border-gray-600 rounded text-white text-sm"
                  />
                  <Button onClick={generateApiKey} className="bg-purple-600 hover:bg-purple-700">
                    <Code className="w-4 h-4 mr-2" />
                    Generate
                  </Button>
                </div>
              </div>

              <div className="p-4 bg-black/30 rounded-lg">
                <h5 className="text-white font-medium mb-3">Example Usage</h5>
                <pre className="text-green-400 text-sm overflow-x-auto">
{`curl -X POST https://api.ghostlayer.app/v1/humanize \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "text": "Your AI-generated text here",
    "style": "academic",
    "intensity": "medium",
    "language": "en"
  }'`}
                </pre>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-white/5 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <BarChart3 className="w-4 h-4 text-blue-400" />
                    <span className="text-white font-medium">Rate Limits</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-300">
                    <div>Free: 100 requests/day</div>
                    <div>Pro: 10,000 requests/day</div>
                    <div>Enterprise: Unlimited</div>
                  </div>
                </div>
                
                <div className="p-4 bg-white/5 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-4 h-4 text-green-400" />
                    <span className="text-white font-medium">Response Time</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-300">
                    <div>Average: 1.2s</div>
                    <div>99th percentile: 3.5s</div>
                    <div>Uptime: 99.9%</div>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                <div className="flex items-center gap-2 mb-2">
                  <Database className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-400 font-semibold text-sm">SDK Libraries</span>
                </div>
                <p className="text-gray-300 text-sm mb-3">
                  Official SDKs available for popular programming languages
                </p>
                <div className="flex gap-2 flex-wrap">
                  {['JavaScript', 'Python', 'PHP', 'Ruby', 'Go', 'Java'].map((lang) => (
                    <Badge key={lang} variant="secondary" className="bg-blue-500/20 text-blue-400">
                      {lang}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Integrations */}
          <TabsContent value="integrations" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-white font-semibold">Tool Integrations</h4>
                <Badge className="bg-orange-500/20 text-orange-400">
                  Coming Soon
                </Badge>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {[
                  { name: 'Google Docs', icon: '📄', status: 'available', description: 'Humanize directly in Google Docs' },
                  { name: 'Notion', icon: '📝', status: 'available', description: 'Seamless Notion integration' },
                  { name: 'WordPress', icon: '🌐', status: 'beta', description: 'WordPress plugin for content creators' },
                  { name: 'Slack', icon: '💬', status: 'coming', description: 'Team collaboration in Slack' },
                  { name: 'Microsoft Word', icon: '📘', status: 'coming', description: 'Word add-in for Office users' },
                  { name: 'Zapier', icon: '⚡', status: 'coming', description: 'Automate with 5000+ apps' }
                ].map((integration) => (
                  <div key={integration.name} className="p-4 bg-white/5 rounded-lg">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-2xl">{integration.icon}</span>
                      <div>
                        <div className="text-white font-medium">{integration.name}</div>
                        <Badge className={
                          integration.status === 'available' ? 'bg-green-500/20 text-green-400' :
                          integration.status === 'beta' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-gray-500/20 text-gray-400'
                        }>
                          {integration.status === 'available' ? 'Available' :
                           integration.status === 'beta' ? 'Beta' : 'Coming Soon'}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-gray-400 text-sm">{integration.description}</p>
                  </div>
                ))}
              </div>

              <div className="p-4 bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-lg border border-orange-500/20">
                <div className="flex items-center gap-2 mb-2">
                  <Workflow className="w-4 h-4 text-orange-400" />
                  <span className="text-orange-400 font-semibold text-sm">Workflow Automation</span>
                </div>
                <p className="text-gray-300 text-sm mb-3">
                  Set up automated workflows to humanize content as it's created across your favorite tools.
                </p>
                <Button className="bg-orange-600 hover:bg-orange-700">
                  <Settings className="w-4 h-4 mr-2" />
                  Configure Workflows
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
