// Language detection utility using pattern matching and character analysis
// This is a client-side implementation that doesn't require external APIs

interface LanguagePattern {
  code: string;
  patterns: RegExp[];
  commonWords: string[];
  characterRanges: { min: number; max: number; weight: number }[];
  stopWords: string[];
}

const LANGUAGE_PATTERNS: LanguagePattern[] = [
  {
    code: 'en',
    patterns: [
      /\b(the|and|or|but|in|on|at|to|for|of|with|by)\b/gi,
      /\b(this|that|these|those|here|there|where|when|what|how)\b/gi,
      /\b(is|are|was|were|be|been|being|have|has|had|do|does|did)\b/gi
    ],
    commonWords: ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were'],
    characterRanges: [
      { min: 0x0041, max: 0x005A, weight: 1 }, // A-Z
      { min: 0x0061, max: 0x007A, weight: 1 }  // a-z
    ],
    stopWords: ['the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at']
  },
  {
    code: 'vi',
    patterns: [
      /\b(và|hoặc|nhưng|trong|trên|tại|để|cho|của|với|bởi)\b/gi,
      /\b(này|đó|những|ở|đây|khi|nào|gì|như|thế|nào)\b/gi,
      /\b(là|được|có|đã|sẽ|đang|rồi|chưa|không|phải)\b/gi,
      /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/gi
    ],
    commonWords: ['và', 'hoặc', 'nhưng', 'trong', 'trên', 'tại', 'để', 'cho', 'của', 'với', 'bởi', 'là', 'được', 'có', 'đã', 'sẽ'],
    characterRanges: [
      { min: 0x0041, max: 0x005A, weight: 0.5 }, // A-Z (lower weight for Vietnamese)
      { min: 0x0061, max: 0x007A, weight: 0.5 }, // a-z
      { min: 0x00C0, max: 0x024F, weight: 2 },   // Latin Extended (Vietnamese diacritics)
      { min: 0x1EA0, max: 0x1EF9, weight: 3 }    // Vietnamese specific diacritics
    ],
    stopWords: ['và', 'của', 'có', 'trong', 'là', 'được', 'một', 'cho', 'với', 'này', 'đó', 'không', 'để', 'các', 'những', 'từ', 'người', 'khi', 'sẽ', 'đã']
  },
  {
    code: 'zh',
    patterns: [
      /[一-龯]/g, // Chinese characters
      /\b(和|或|但|在|上|到|为|的|与|由)\b/gi,
      /\b(这|那|些|里|哪|什么|如何|怎么)\b/gi,
      /\b(是|有|没|了|会|能|要|可以|应该)\b/gi
    ],
    commonWords: ['的', '和', '在', '是', '有', '了', '不', '人', '我', '他', '她', '它', '们', '这', '那', '一'],
    characterRanges: [
      { min: 0x4E00, max: 0x9FFF, weight: 5 }, // CJK Unified Ideographs
      { min: 0x3400, max: 0x4DBF, weight: 3 }, // CJK Extension A
      { min: 0x20000, max: 0x2A6DF, weight: 3 } // CJK Extension B
    ],
    stopWords: ['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去']
  },
  {
    code: 'zh-tw',
    patterns: [
      /[一-龯]/g, // Chinese characters (Traditional)
      /\b(和|或|但|在|上|到|為|的|與|由)\b/gi,
      /\b(這|那|些|裡|哪|什麼|如何|怎麼)\b/gi,
      /\b(是|有|沒|了|會|能|要|可以|應該)\b/gi,
      /[繁體]/g // Traditional specific characters
    ],
    commonWords: ['的', '和', '在', '是', '有', '了', '不', '人', '我', '他', '她', '它', '們', '這', '那', '一'],
    characterRanges: [
      { min: 0x4E00, max: 0x9FFF, weight: 5 }, // CJK Unified Ideographs
      { min: 0x3400, max: 0x4DBF, weight: 3 }, // CJK Extension A
      { min: 0xF900, max: 0xFAFF, weight: 4 }  // CJK Compatibility Ideographs (more common in Traditional)
    ],
    stopWords: ['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一個', '上', '也', '很', '到', '說', '要', '去']
  },
  {
    code: 'es',
    patterns: [
      /\b(el|la|los|las|un|una|y|o|pero|en|de|a|para|con|por)\b/gi,
      /\b(este|esta|estos|estas|ese|esa|esos|esas|donde|cuando|que|como)\b/gi,
      /\b(es|son|era|fueron|ser|estar|tener|hacer|decir|ver)\b/gi,
      /[ñáéíóúü]/gi
    ],
    commonWords: ['el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para'],
    characterRanges: [
      { min: 0x0041, max: 0x005A, weight: 0.5 }, // A-Z
      { min: 0x0061, max: 0x007A, weight: 0.5 }, // a-z
      { min: 0x00C0, max: 0x024F, weight: 2 },   // Latin Extended (Spanish diacritics)
      { min: 0x00F1, max: 0x00F1, weight: 3 }    // ñ
    ],
    stopWords: ['el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para']
  },
  {
    code: 'fr',
    patterns: [
      /\b(le|la|les|un|une|et|ou|mais|dans|sur|à|pour|de|avec|par)\b/gi,
      /\b(ce|cette|ces|où|quand|que|comment|qui|quoi)\b/gi,
      /\b(est|sont|était|étaient|être|avoir|faire|dire|voir)\b/gi,
      /[àâäéèêëïîôöùûüÿç]/gi
    ],
    commonWords: ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se'],
    characterRanges: [
      { min: 0x0041, max: 0x005A, weight: 0.5 }, // A-Z
      { min: 0x0061, max: 0x007A, weight: 0.5 }, // a-z
      { min: 0x00C0, max: 0x024F, weight: 2 },   // Latin Extended (French diacritics)
      { min: 0x00E7, max: 0x00E7, weight: 3 }    // ç
    ],
    stopWords: ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se']
  },
  {
    code: 'ja',
    patterns: [
      /[\u3040-\u309F]/g, // Hiragana
      /[\u30A0-\u30FF]/g, // Katakana
      /[\u4E00-\u9FAF]/g, // Kanji
      /\b(は|が|を|に|で|と|の|から|まで|より)\b/gi,
      /\b(です|ます|である|だ|する|いる|ある|なる)\b/gi
    ],
    commonWords: ['の', 'に', 'は', 'を', 'た', 'が', 'で', 'て', 'と', 'し', 'れ', 'さ', 'ある', 'いる', 'も', 'する', 'から', 'な', 'こと', 'として'],
    characterRanges: [
      { min: 0x3040, max: 0x309F, weight: 5 }, // Hiragana
      { min: 0x30A0, max: 0x30FF, weight: 4 }, // Katakana
      { min: 0x4E00, max: 0x9FAF, weight: 5 }  // Kanji
    ],
    stopWords: ['の', 'に', 'は', 'を', 'た', 'が', 'で', 'て', 'と', 'し', 'れ', 'さ', 'ある', 'いる', 'も', 'する', 'から', 'な', 'こと', 'として']
  }
];

export async function detectLanguage(text: string): Promise<string> {
  if (!text || text.trim().length < 10) {
    return 'en'; // Default to English for short texts
  }

  const cleanText = text.toLowerCase().trim();
  const scores: { [key: string]: number } = {};

  // Initialize scores
  LANGUAGE_PATTERNS.forEach(lang => {
    scores[lang.code] = 0;
  });

  // Quick detection for specific character sets
  // Japanese detection (highest priority due to unique character sets)
  if (/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(text)) {
    return 'ja';
  }

  // Chinese detection
  if (/[\u4e00-\u9fff]/.test(text)) {
    // Check for Traditional vs Simplified
    const traditionalChars = /[繁體華語臺灣]/g;
    if (traditionalChars.test(text)) {
      return 'zh-tw';
    }
    return 'zh';
  }

  // Vietnamese detection (diacritics)
  if (/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i.test(text)) {
    return 'vi';
  }

  // Spanish detection (ñ and specific patterns)
  if (/[ñáéíóúü]/i.test(text) || /\b(el|la|los|las|que|de|en|un|una|es|son|por|para|con|se|no|te|lo|le|da|su|pero|más|todo|muy|año|día|vez|tiempo|casa|vida|mundo|país|parte|lugar|forma|caso|grupo|empresa|sistema|trabajo|gobierno|número|punto|mano|ojo|momento|manera|persona|año|día|vez|tiempo|casa|vida|mundo|país|parte|lugar|forma|caso|grupo|empresa|sistema|trabajo|gobierno|número|punto|mano|ojo|momento|manera|persona)\b/gi.test(text)) {
    return 'es';
  }

  // French detection (specific diacritics and patterns)
  if (/[àâäéèêëïîôöùûüÿç]/i.test(text) || /\b(le|la|les|de|et|à|un|une|il|elle|être|avoir|que|pour|dans|ce|son|sur|avec|ne|se|pas|tout|mais|faire|dire|voir|savoir|prendre|venir|vouloir|pouvoir|falloir|devoir|croire|trouver|donner|parler|aimer|porter|vivre|mourir|naître|partir|sortir|entrer|monter|descendre|passer|rester|devenir|tenir|mettre|laisser|suivre|connaître|paraître|ouvrir|couvrir|offrir|souffrir|cueillir|assaillir|bouillir|dormir|mentir|sentir|servir|partir|sortir|fuir|courir|mourir|acquérir|conquérir|requérir|vêtir|battre|mettre|permettre|promettre|admettre|commettre|omettre|soumettre|transmettre|remettre|compromettre|démettre|émettre|réadmettre|readmettre)\b/gi.test(text)) {
    return 'fr';
  }

  // Analyze each language pattern
  for (const lang of LANGUAGE_PATTERNS) {
    let langScore = 0;

    // Pattern matching
    lang.patterns.forEach(pattern => {
      const matches = cleanText.match(pattern);
      if (matches) {
        langScore += matches.length * 2;
      }
    });

    // Common words detection
    const words = cleanText.split(/\s+/);
    lang.commonWords.forEach(commonWord => {
      const wordCount = words.filter(word => 
        word.replace(/[^\w]/g, '') === commonWord
      ).length;
      langScore += wordCount * 3;
    });

    // Stop words analysis
    lang.stopWords.forEach(stopWord => {
      const stopWordCount = words.filter(word => 
        word.replace(/[^\w]/g, '') === stopWord
      ).length;
      langScore += stopWordCount * 1.5;
    });

    // Character range analysis
    for (const char of text) {
      const charCode = char.codePointAt(0);
      if (charCode) {
        lang.characterRanges.forEach(range => {
          if (charCode >= range.min && charCode <= range.max) {
            langScore += range.weight;
          }
        });
      }
    }

    // Normalize score by text length
    scores[lang.code] = langScore / text.length;
  }

  // Special handling for Chinese variants
  if (scores['zh'] > 0 || scores['zh-tw'] > 0) {
    // Check for Traditional Chinese specific characters
    const traditionalChars = /[繁體華語臺灣]/g;
    const simplifiedChars = /[简体中国]/g;
    
    const traditionalMatches = text.match(traditionalChars);
    const simplifiedMatches = text.match(simplifiedChars);
    
    if (traditionalMatches && traditionalMatches.length > 0) {
      scores['zh-tw'] += traditionalMatches.length * 2;
    }
    
    if (simplifiedMatches && simplifiedMatches.length > 0) {
      scores['zh'] += simplifiedMatches.length * 2;
    }
  }

  // Find the language with the highest score
  let detectedLanguage = 'en';
  let maxScore = scores['en'];

  Object.entries(scores).forEach(([lang, score]) => {
    if (score > maxScore) {
      maxScore = score;
      detectedLanguage = lang;
    }
  });

  // Confidence threshold - if no language scores significantly, default to English
  if (maxScore < 0.1) {
    return 'en';
  }

  return detectedLanguage;
}

export function getLanguageConfidence(text: string, detectedLanguage: string): number {
  if (!text || text.trim().length < 10) {
    return 0;
  }

  const langPattern = LANGUAGE_PATTERNS.find(lang => lang.code === detectedLanguage);
  if (!langPattern) {
    return 0;
  }

  let confidence = 0;
  const cleanText = text.toLowerCase().trim();
  const words = cleanText.split(/\s+/);

  // Calculate confidence based on pattern matches
  langPattern.patterns.forEach(pattern => {
    const matches = cleanText.match(pattern);
    if (matches) {
      confidence += (matches.length / words.length) * 0.3;
    }
  });

  // Calculate confidence based on common words
  const commonWordMatches = words.filter(word => 
    langPattern.commonWords.includes(word.replace(/[^\w]/g, ''))
  ).length;
  confidence += (commonWordMatches / words.length) * 0.4;

  // Calculate confidence based on character ranges
  let charScore = 0;
  for (const char of text) {
    const charCode = char.codePointAt(0);
    if (charCode) {
      langPattern.characterRanges.forEach(range => {
        if (charCode >= range.min && charCode <= range.max) {
          charScore += range.weight;
        }
      });
    }
  }
  confidence += Math.min((charScore / text.length) * 0.3, 0.3);

  return Math.min(confidence, 1);
}

export function getSupportedLanguages(): string[] {
  return LANGUAGE_PATTERNS.map(lang => lang.code);
}

export function isLanguageSupported(languageCode: string): boolean {
  return LANGUAGE_PATTERNS.some(lang => lang.code === languageCode);
}
