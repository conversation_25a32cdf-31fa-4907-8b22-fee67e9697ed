// Test the API endpoint to verify it works
const https = require('https');
const http = require('http');

async function testAPI() {
  console.log('=== TESTING API ENDPOINT ===\n');
  
  const testText = "This is a very good example of how AI systems work with new technology.";
  const options = {
    intensity: 'medium',
    style: 'balanced',
    preserveFormat: true,
    addVariations: false
  };

  try {
    console.log('Sending request to API...');

    const postData = JSON.stringify({
      text: testText,
      options: options
    });

    const options_req = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/process',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const result = await new Promise((resolve, reject) => {
      const req = http.request(options_req, (res) => {
        console.log('Response status:', res.statusCode);

        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            const parsed = JSON.parse(data);
            if (res.statusCode !== 200) {
              console.error('Error response:', parsed);
              reject(new Error('API Error: ' + JSON.stringify(parsed)));
              return;
            }
            resolve(parsed);
          } catch (e) {
            reject(new Error('Failed to parse response: ' + data));
          }
        });
      });

      req.on('error', (e) => {
        reject(e);
      });

      req.write(postData);
      req.end();
    });
    
    console.log('\n=== RESULTS ===');
    console.log('Original text:', testText);
    console.log('Humanized text:', result.humanizedText);
    console.log('Processing time:', result.processingTime + 'ms');
    console.log('Improvement score:', result.improvementScore);
    console.log('Detection score:', result.detectionScore);
    console.log('Confidence:', result.confidence);
    console.log('Readability score:', result.readabilityScore);
    
    // Check if the result looks good
    const hasProtectedTerms = result.humanizedText.includes('AI');
    const hasReasonableLength = result.humanizedText.length > 0;
    const hasNoErrors = !result.humanizedText.includes('undefined') && !result.humanizedText.includes('sentences is not defined');
    
    console.log('\n=== VALIDATION ===');
    console.log('Protected terms preserved:', hasProtectedTerms ? '✓' : '✗');
    console.log('Reasonable length:', hasReasonableLength ? '✓' : '✗');
    console.log('No errors:', hasNoErrors ? '✓' : '✗');
    
    if (hasProtectedTerms && hasReasonableLength && hasNoErrors) {
      console.log('\n🎉 API TEST PASSED!');
    } else {
      console.log('\n❌ API TEST FAILED!');
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Run the test
testAPI();
