Add the following features to the GhostLayer project:

1. **Testimonials Section**: Create a testimonials component that displays user reviews and feedback about the AI humanization service. Include:
   - User name, role/title, and optional avatar
   - Star ratings or other rating system
   - Testimonial text with proper formatting
   - Responsive design that works on all devices
   - Option to cycle through multiple testimonials

2. **SEO (Search Engine Optimization)**: Implement comprehensive SEO optimization including:
   - Meta tags (title, description, keywords) for all pages
   - Open Graph tags for social media sharing
   - Structured data/schema markup for better search engine understanding
   - Optimized heading hierarchy (H1, H2, H3, etc.)
   - Alt text for images
   - XML sitemap generation
   - Robots.txt file
   - Page loading speed optimization

3. **AEO (Answer Engine Optimization)**: Optimize content for AI-powered search engines and voice assistants:
   - FAQ sections with clear question-answer format
   - Featured snippet optimization
   - Conversational content structure
   - Long-tail keyword integration
   - Content that directly answers common user queries about AI text humanization

4. **GEO (Generative Engine Optimization)**: Optimize for AI-generated search results and summaries:
   - Clear, authoritative content that AI models can easily cite
   - Structured information that can be used in AI-generated responses
   - Brand mention optimization
   - Content formatting that works well with generative AI platforms
   - Integration with emerging AI search platforms

Please implement these features while maintaining the existing dark theme and user experience of the application.