'use client';

import { useEffect } from 'react';

interface SecurityProviderProps {
  children: React.ReactNode;
}

/**
 * Security Provider Component
 * Initializes client-side security measures and monitoring
 */
export default function SecurityProvider({ children }: SecurityProviderProps) {
  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return;

    // Dynamically import security functions to avoid build issues
    import('@/lib/security').then(({ initializeSecurity, validateSecurityHeaders }) => {
      // Initialize security measures on client-side
      initializeSecurity();

      // Validate security headers (development check)
      if (process.env.NODE_ENV === 'development') {
        validateSecurityHeaders();
      }
    }).catch(error => {
      console.warn('Failed to initialize security:', error);
    });

    // Add global error handler for security-related errors
    const handleGlobalError = (event: ErrorEvent) => {
      // Log security-related errors
      if (event.error?.name === 'SecurityError' ||
          event.message?.includes('Content Security Policy')) {
        console.warn('Security-related error detected:', event.error || event.message);
      }
    };

    // Add unhandled promise rejection handler
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.warn('Unhandled promise rejection:', event.reason);
    };

    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // Cleanup event listeners
    return () => {
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return <>{children}</>;
}
