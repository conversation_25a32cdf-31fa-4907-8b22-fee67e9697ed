import React from 'react';

interface LoadingSpinnerProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
}

export default function LoadingSpinner({ message = 'Loading...', size = 'md' }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <div className={`animate-spin rounded-full border-2 border-gray-600 border-t-blue-400 ${sizeClasses[size]} mb-4`}></div>
      <p className="text-gray-400 text-sm">{message}</p>
    </div>
  );
}
