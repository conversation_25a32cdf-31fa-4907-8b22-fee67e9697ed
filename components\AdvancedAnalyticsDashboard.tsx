'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Globe, 
  Target,
  Eye,
  Share2,
  Clock,
  Zap,
  Award,
  DollarSign,
  Activity,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Minus,
  MapPin,
  Smartphone,
  Monitor,
  Tablet
} from 'lucide-react';
import { analytics } from '@/lib/analytics';

interface AnalyticsData {
  overview: {
    totalUsers: number;
    activeUsers: number;
    totalProcessing: number;
    avgImprovementScore: number;
    conversionRate: number;
    revenue: number;
  };
  growth: {
    userGrowth: number;
    processingGrowth: number;
    revenueGrowth: number;
    retentionRate: number;
  };
  geographic: Array<{
    country: string;
    users: number;
    flag: string;
    growth: number;
  }>;
  devices: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  languages: Array<{
    language: string;
    usage: number;
    flag: string;
  }>;
  viralMetrics: {
    shareRate: number;
    viralCoefficient: number;
    referralConversion: number;
    socialShares: number;
  };
}

export default function AdvancedAnalyticsDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('7d');
  const [isLoading, setIsLoading] = useState(false);
  // Simplified initial data to prevent build hanging
  const [data, setData] = useState<AnalyticsData>({
    overview: {
      totalUsers: 47832,
      activeUsers: 12456,
      totalProcessing: 234567,
      avgImprovementScore: 87.3,
      conversionRate: 23.4,
      revenue: 45678.90
    },
    growth: {
      userGrowth: 34.2,
      processingGrowth: 67.8,
      revenueGrowth: 89.1,
      retentionRate: 78.5
    },
    geographic: [
      { country: 'United States', users: 18432, flag: '🇺🇸', growth: 23.4 },
      { country: 'United Kingdom', users: 8765, flag: '🇬🇧', growth: 18.7 },
      { country: 'Canada', users: 5432, flag: '🇨🇦', growth: 31.2 },
      { country: 'Australia', users: 3456, flag: '🇦🇺', growth: 28.9 }
    ],
    devices: {
      desktop: 65.4,
      mobile: 28.7,
      tablet: 5.9
    },
    languages: [
      { language: 'English', usage: 68.4, flag: '🇺🇸' },
      { language: 'Spanish', usage: 12.7, flag: '🇪🇸' },
      { language: 'French', usage: 8.9, flag: '🇫🇷' },
      { language: 'Chinese', usage: 5.2, flag: '🇨🇳' }
    ],
    viralMetrics: {
      shareRate: 34.7,
      viralCoefficient: 1.8,
      referralConversion: 23.4,
      socialShares: 8765
    }
  });

  const refreshData = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      analytics.trackShare('analytics_refreshed');
    } catch (error) {
      console.error('Failed to refresh analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const exportData = () => {
    analytics.trackShare('analytics_exported');
    // Handle data export
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <ArrowUp className="w-4 h-4 text-green-400" />;
    if (growth < 0) return <ArrowDown className="w-4 h-4 text-red-400" />;
    return <Minus className="w-4 h-4 text-gray-400" />;
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-400';
    if (growth < 0) return 'text-red-400';
    return 'text-gray-400';
  };

  return (
    <Card className="bg-white/5 backdrop-blur-lg border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-white">
          <div className="p-2 bg-blue-500/20 rounded-lg">
            <BarChart3 className="w-5 h-5 text-blue-400" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold">Advanced Analytics</h3>
            <p className="text-gray-400 text-sm font-normal">Comprehensive insights and business intelligence</p>
          </div>
          <div className="flex items-center gap-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-1 bg-black/30 border border-gray-600 rounded text-white text-sm"
            >
              <option value="24h">Last 24 hours</option>
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
            <Button size="sm" onClick={refreshData} disabled={isLoading} className="bg-blue-600 hover:bg-blue-700">
              <RefreshCw className={`w-4 h-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button size="sm" onClick={exportData} variant="outline" className="border-gray-600 text-gray-300">
              <Download className="w-4 h-4 mr-1" />
              Export
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5 bg-slate-800/50">
            <TabsTrigger value="overview" className="text-white">📊 Overview</TabsTrigger>
            <TabsTrigger value="users" className="text-white">👥 Users</TabsTrigger>
            <TabsTrigger value="viral" className="text-white">🚀 Viral</TabsTrigger>
            <TabsTrigger value="revenue" className="text-white">💰 Revenue</TabsTrigger>
            <TabsTrigger value="realtime" className="text-white">⚡ Real-time</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="mt-6">
            <div className="space-y-6">
              {/* Key Metrics */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="p-4 bg-white/5 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <Users className="w-5 h-5 text-blue-400" />
                    <div className={`flex items-center gap-1 ${getGrowthColor(data.growth.userGrowth)}`}>
                      {getGrowthIcon(data.growth.userGrowth)}
                      <span className="text-sm">{data.growth.userGrowth}%</span>
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-white">{data.overview.totalUsers.toLocaleString()}</div>
                  <div className="text-gray-400 text-sm">Total Users</div>
                </div>

                <div className="p-4 bg-white/5 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <Activity className="w-5 h-5 text-green-400" />
                    <div className={`flex items-center gap-1 ${getGrowthColor(data.growth.processingGrowth)}`}>
                      {getGrowthIcon(data.growth.processingGrowth)}
                      <span className="text-sm">{data.growth.processingGrowth}%</span>
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-white">{data.overview.totalProcessing.toLocaleString()}</div>
                  <div className="text-gray-400 text-sm">Text Processed</div>
                </div>

                <div className="p-4 bg-white/5 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <Target className="w-5 h-5 text-purple-400" />
                    <div className="flex items-center gap-1 text-green-400">
                      <ArrowUp className="w-4 h-4" />
                      <span className="text-sm">2.3%</span>
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-white">{data.overview.avgImprovementScore}%</div>
                  <div className="text-gray-400 text-sm">Avg Improvement</div>
                </div>

                <div className="p-4 bg-white/5 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <DollarSign className="w-5 h-5 text-yellow-400" />
                    <div className={`flex items-center gap-1 ${getGrowthColor(data.growth.revenueGrowth)}`}>
                      {getGrowthIcon(data.growth.revenueGrowth)}
                      <span className="text-sm">{data.growth.revenueGrowth}%</span>
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-white">${data.overview.revenue.toLocaleString()}</div>
                  <div className="text-gray-400 text-sm">Revenue</div>
                </div>
              </div>

              {/* Geographic Distribution */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="bg-white/5 border-white/10">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Globe className="w-5 h-5 text-blue-400" />
                      Geographic Distribution
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {data.geographic.slice(0, 6).map((country, index) => (
                        <div key={country.country} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <span className="text-lg">{country.flag}</span>
                            <span className="text-white">{country.country}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-gray-400">{country.users.toLocaleString()}</span>
                            <div className={`flex items-center gap-1 ${getGrowthColor(country.growth)}`}>
                              {getGrowthIcon(country.growth)}
                              <span className="text-sm">{country.growth}%</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-white/5 border-white/10">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Globe className="w-5 h-5 text-green-400" />
                      Language Usage
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {data.languages.map((lang) => (
                        <div key={lang.language} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <span className="text-lg">{lang.flag}</span>
                            <span className="text-white">{lang.language}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-20 bg-gray-700 rounded-full h-2">
                              <div 
                                className="bg-green-500 h-2 rounded-full"
                                style={{ width: `${lang.usage}%` }}
                              />
                            </div>
                            <span className="text-gray-400 text-sm w-12">{lang.usage}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Device Distribution */}
              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Smartphone className="w-5 h-5 text-purple-400" />
                    Device Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-white/5 rounded-lg">
                      <Monitor className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-white">{data.devices.desktop}%</div>
                      <div className="text-gray-400 text-sm">Desktop</div>
                    </div>
                    <div className="text-center p-4 bg-white/5 rounded-lg">
                      <Smartphone className="w-8 h-8 text-green-400 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-white">{data.devices.mobile}%</div>
                      <div className="text-gray-400 text-sm">Mobile</div>
                    </div>
                    <div className="text-center p-4 bg-white/5 rounded-lg">
                      <Tablet className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-white">{data.devices.tablet}%</div>
                      <div className="text-gray-400 text-sm">Tablet</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users" className="mt-6">
            <div className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-400">{data.overview.activeUsers.toLocaleString()}</div>
                  <div className="text-gray-400 text-sm">Active Users</div>
                </div>
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-400">{data.growth.retentionRate}%</div>
                  <div className="text-gray-400 text-sm">Retention Rate</div>
                </div>
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-purple-400">{data.overview.conversionRate}%</div>
                  <div className="text-gray-400 text-sm">Conversion Rate</div>
                </div>
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-yellow-400">4.2</div>
                  <div className="text-gray-400 text-sm">Avg Session</div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Viral Tab */}
          <TabsContent value="viral" className="mt-6">
            <div className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-pink-400">{data.viralMetrics.shareRate}%</div>
                  <div className="text-gray-400 text-sm">Share Rate</div>
                </div>
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-red-400">{data.viralMetrics.viralCoefficient}</div>
                  <div className="text-gray-400 text-sm">Viral Coefficient</div>
                </div>
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-orange-400">{data.viralMetrics.referralConversion}%</div>
                  <div className="text-gray-400 text-sm">Referral Conversion</div>
                </div>
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-400">{data.viralMetrics.socialShares.toLocaleString()}</div>
                  <div className="text-gray-400 text-sm">Social Shares</div>
                </div>
              </div>

              <div className="p-6 bg-gradient-to-r from-pink-500/10 to-purple-500/10 rounded-lg border border-pink-500/20">
                <h4 className="text-white font-semibold mb-3">Viral Growth Analysis</h4>
                <p className="text-gray-300 text-sm mb-4">
                  Your viral coefficient of {data.viralMetrics.viralCoefficient} indicates strong viral growth. 
                  Each user is bringing in {data.viralMetrics.viralCoefficient} new users on average.
                </p>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-green-400 font-semibold">✅ Strong Indicators</div>
                    <ul className="text-gray-300 text-sm mt-2 space-y-1">
                      <li>• High share rate ({data.viralMetrics.shareRate}%)</li>
                      <li>• Viral coefficient &gt; 1.0</li>
                      <li>• Growing social engagement</li>
                    </ul>
                  </div>
                  <div>
                    <div className="text-yellow-400 font-semibold">⚠️ Optimization Areas</div>
                    <ul className="text-gray-300 text-sm mt-2 space-y-1">
                      <li>• Improve referral conversion</li>
                      <li>• Increase social platform diversity</li>
                      <li>• Enhance sharing incentives</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Revenue Tab */}
          <TabsContent value="revenue" className="mt-6">
            <div className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-400">${data.overview.revenue.toLocaleString()}</div>
                  <div className="text-gray-400 text-sm">Total Revenue</div>
                </div>
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-400">$23.45</div>
                  <div className="text-gray-400 text-sm">ARPU</div>
                </div>
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-purple-400">$156.78</div>
                  <div className="text-gray-400 text-sm">LTV</div>
                </div>
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-yellow-400">$12.34</div>
                  <div className="text-gray-400 text-sm">CAC</div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Real-time Tab */}
          <TabsContent value="realtime" className="mt-6">
            <div className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-400">1,247</div>
                  <div className="text-gray-400 text-sm">Online Now</div>
                </div>
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-400">89</div>
                  <div className="text-gray-400 text-sm">Processing Now</div>
                </div>
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-purple-400">23</div>
                  <div className="text-gray-400 text-sm">Shares/min</div>
                </div>
                <div className="p-4 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-yellow-400">156</div>
                  <div className="text-gray-400 text-sm">New Users/hr</div>
                </div>
              </div>

              <div className="p-4 bg-white/5 rounded-lg">
                <h4 className="text-white font-semibold mb-3">Live Activity Feed</h4>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {[
                    { time: '2s ago', action: 'New user signed up from Spain', type: 'user' },
                    { time: '5s ago', action: 'Content shared on Twitter', type: 'share' },
                    { time: '8s ago', action: 'Text processed: 95% improvement', type: 'process' },
                    { time: '12s ago', action: 'Premium subscription purchased', type: 'revenue' },
                    { time: '15s ago', action: 'Referral conversion completed', type: 'referral' },
                    { time: '18s ago', action: 'Extension installed', type: 'install' }
                  ].map((activity, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-white/5 rounded text-sm">
                      <span className="text-gray-300">{activity.action}</span>
                      <span className="text-gray-500">{activity.time}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
