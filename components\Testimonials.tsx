'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Star, Quote, CheckCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Testimonial } from '@/types';

const testimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'Research Director',
    company: '',
    rating: 5,
    text: '<PERSON><PERSON><PERSON><PERSON> has revolutionized how we prepare our research papers for publication. The AI humanization is so sophisticated that it maintains academic rigor while making our content more accessible.',
    date: 'January 15, 2024',
    verified: true
  },
  {
    id: '2',
    name: '<PERSON>',
    role: 'Content Marketing Manager',
    company: 'TechFlow Inc.',
    rating: 5,
    text: 'As someone who works with AI-generated content daily, <PERSON><PERSON><PERSON><PERSON> is a game-changer. It transforms robotic text into engaging, human-like content that resonates with our audience.',
    date: 'February 10, 2024',
    verified: true
  },
  {
    id: '3',
    name: '<PERSON>',
    role: 'Freelance Writer',
    company: 'Independent',
    rating: 5,
    text: 'I use Ghost<PERSON>ayer to refine AI-assisted drafts for my clients. The quality improvement is remarkable - it adds the human touch that makes content truly compelling.',
    date: 'April 8, 2024',
    verified: true
  },
  {
    id: '4',
    name: 'James Liu',
    role: 'Computer Science PhD Student',
    company: 'MIT',
    rating: 5,
    text: 'The technical sophistication behind GhostLayer is impressive. It understands context and nuance in ways that other tools simply cannot match.',
    date: 'March 5, 2025',
    verified: true
  },
  {
    id: '5',
    name: 'Anna Kowalski',
    role: 'Digital Marketing Specialist',
    company: 'Growth Labs',
    rating: 5,
    text: 'GhostLayer has become an essential part of our content workflow. It helps us create authentic, engaging content that performs better across all our channels.',
    date: 'May 3, 2025',
    verified: true
  }
];

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  // Handle mounting to prevent hydration issues
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Auto-advance testimonials
  useEffect(() => {
    if (!isAutoPlaying || !isMounted) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, isMounted]);

  const nextTestimonial = () => {
    if (!isMounted) return;
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const prevTestimonial = () => {
    if (!isMounted) return;
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const goToTestimonial = (index: number) => {
    if (!isMounted) return;
    setCurrentIndex(index);
    setIsAutoPlaying(false);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'
        }`}
      />
    ));
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase();
  };

  // Prevent hydration issues by not rendering until mounted
  if (!isMounted) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              What Our Users Say
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Discover how GhostLayer is transforming the way professionals work with AI-generated content
            </p>
          </div>
          <div className="animate-pulse">
            <div className="bg-white/5 backdrop-blur-lg border-white/10 rounded-lg h-64 mb-8"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 px-4">
      <div className="container mx-auto max-w-6xl">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-white mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            What Our Users Say
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Discover how GhostLayer is transforming the way professionals work with AI-generated content
          </p>
        </div>

        {/* Main Testimonial Display */}
        <div className="relative mb-8">
          <Card className="bg-white/5 backdrop-blur-lg border-white/10 p-8 md:p-12">
            <CardContent className="p-0">
              <div className="flex flex-col md:flex-row items-start gap-8">
                {/* Avatar and User Info */}
                <div className="flex-shrink-0 text-center md:text-left">
                  <Avatar className="w-20 h-20 mx-auto md:mx-0 mb-4 ring-2 ring-blue-500/30">
                    <AvatarImage 
                      src={testimonials[currentIndex].avatar} 
                      alt={testimonials[currentIndex].name}
                    />
                    <AvatarFallback className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-lg font-semibold">
                      {getInitials(testimonials[currentIndex].name)}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-center md:justify-start gap-2">
                      <h3 className="text-lg font-semibold text-white">
                        {testimonials[currentIndex].name}
                      </h3>
                      {testimonials[currentIndex].verified && (
                        <CheckCircle className="w-4 h-4 text-blue-400" />
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-400">
                      {testimonials[currentIndex].role}
                    </p>
                    
                    {testimonials[currentIndex].company && (
                      <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                        {testimonials[currentIndex].company}
                      </Badge>
                    )}
                    
                    <div className="flex items-center justify-center md:justify-start gap-1 mt-2">
                      {renderStars(testimonials[currentIndex].rating)}
                    </div>
                  </div>
                </div>

                {/* Testimonial Content */}
                <div className="flex-1">
                  <Quote className="w-8 h-8 text-blue-400 mb-4 opacity-60" />
                  <blockquote className="text-lg text-gray-200 leading-relaxed mb-4">
                    "{testimonials[currentIndex].text}"
                  </blockquote>
                  <p className="text-sm text-gray-500">
                    {testimonials[currentIndex].date}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Navigation Buttons */}
          <Button
            variant="outline"
            size="icon"
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/10 border-white/20 hover:bg-white/20 text-white"
            onClick={prevTestimonial}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/10 border-white/20 hover:bg-white/20 text-white"
            onClick={nextTestimonial}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        {/* Testimonial Indicators */}
        <div className="flex justify-center gap-2 mb-8">
          {testimonials.map((_, index) => (
            <button
              key={index}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-blue-500 scale-125'
                  : 'bg-gray-600 hover:bg-gray-500'
              }`}
              onClick={() => goToTestimonial(index)}
            />
          ))}
        </div>

        {/* Compact Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.slice(0, 3).map((testimonial, index) => (
            <Card 
              key={testimonial.id}
              className="bg-white/5 backdrop-blur-lg border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer"
              onClick={() => goToTestimonial(index)}
            >
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <Avatar className="w-12 h-12 ring-1 ring-blue-500/30">
                    <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                    <AvatarFallback className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm">
                      {getInitials(testimonial.name)}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="text-sm font-semibold text-white truncate">
                        {testimonial.name}
                      </h4>
                      {testimonial.verified && (
                        <CheckCircle className="w-3 h-3 text-blue-400 flex-shrink-0" />
                      )}
                    </div>
                    
                    <p className="text-xs text-gray-400 mb-2 truncate">
                      {testimonial.role}
                    </p>
                    
                    <div className="flex items-center gap-1 mb-3">
                      {renderStars(testimonial.rating)}
                    </div>
                    
                    <p className="text-sm text-gray-300 line-clamp-3">
                      "{testimonial.text}"
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Auto-play Control */}
        <div className="text-center mt-8">
          <Button
            variant="ghost"
            size="sm"
            className="text-gray-400 hover:text-white"
            onClick={() => setIsAutoPlaying(!isAutoPlaying)}
          >
            {isAutoPlaying ? 'Pause Auto-play' : 'Resume Auto-play'}
          </Button>
        </div>
      </div>
    </section>
  );
}
