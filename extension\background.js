// Background script for GhostLayer Chrome Extension
// Handles context menus, keyboard shortcuts, and communication with content scripts

// Create context menu when extension is installed
chrome.runtime.onInstalled.addListener(() => {
  // Create main context menu item
  chrome.contextMenus.create({
    id: 'humanize-text',
    title: '🤖➡️👤 Humanize with <PERSON><PERSON>ayer',
    contexts: ['selection']
  });

  // Create submenu for different styles
  chrome.contextMenus.create({
    id: 'humanize-academic',
    parentId: 'humanize-text',
    title: '📚 Academic Style',
    contexts: ['selection']
  });

  chrome.contextMenus.create({
    id: 'humanize-professional',
    parentId: 'humanize-text',
    title: '💼 Professional Style',
    contexts: ['selection']
  });

  chrome.contextMenus.create({
    id: 'humanize-creative',
    parentId: 'humanize-text',
    title: '🎨 Creative Style',
    contexts: ['selection']
  });

  chrome.contextMenus.create({
    id: 'humanize-technical',
    parentId: 'humanize-text',
    title: '⚙️ Technical Style',
    contexts: ['selection']
  });

  // Separator
  chrome.contextMenus.create({
    id: 'separator1',
    parentId: 'humanize-text',
    type: 'separator',
    contexts: ['selection']
  });

  // Quick actions
  chrome.contextMenus.create({
    id: 'open-ghostlayer',
    parentId: 'humanize-text',
    title: '🌐 Open GhostLayer Website',
    contexts: ['selection']
  });

  chrome.contextMenus.create({
    id: 'share-result',
    parentId: 'humanize-text',
    title: '📤 Share Transformation',
    contexts: ['selection']
  });

  // Set default settings
  chrome.storage.sync.set({
    defaultStyle: 'academic',
    defaultIntensity: 'medium',
    autoDetectLanguage: true,
    showNotifications: true,
    trackUsage: true
  });

  // Track installation
  trackEvent('extension_installed', {
    version: chrome.runtime.getManifest().version,
    timestamp: Date.now()
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  const selectedText = info.selectionText;
  
  if (!selectedText || selectedText.trim().length === 0) {
    showNotification('Please select some text to humanize', 'warning');
    return;
  }

  switch (info.menuItemId) {
    case 'humanize-academic':
      humanizeText(selectedText, 'academic', tab);
      break;
    case 'humanize-professional':
      humanizeText(selectedText, 'professional', tab);
      break;
    case 'humanize-creative':
      humanizeText(selectedText, 'creative', tab);
      break;
    case 'humanize-technical':
      humanizeText(selectedText, 'technical', tab);
      break;
    case 'open-ghostlayer':
      chrome.tabs.create({ url: 'https://ghostlayer.app' });
      break;
    case 'share-result':
      shareTransformation(selectedText, tab);
      break;
  }
});

// Handle keyboard shortcuts
chrome.commands.onCommand.addListener((command) => {
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    const tab = tabs[0];
    
    switch (command) {
      case 'humanize-selected':
        chrome.tabs.sendMessage(tab.id, { action: 'getSelectedText' }, (response) => {
          if (response && response.selectedText) {
            chrome.storage.sync.get(['defaultStyle'], (result) => {
              humanizeText(response.selectedText, result.defaultStyle || 'academic', tab);
            });
          } else {
            showNotification('Please select some text first', 'warning');
          }
        });
        break;
      case 'toggle-widget':
        chrome.tabs.sendMessage(tab.id, { action: 'toggleWidget' });
        break;
    }
  });
});

// Main text humanization function
async function humanizeText(text, style, tab) {
  try {
    showNotification('Humanizing text...', 'info');
    
    // Get user preferences
    const settings = await chrome.storage.sync.get([
      'defaultIntensity',
      'autoDetectLanguage',
      'trackUsage'
    ]);

    // Prepare processing options
    const options = {
      style: style,
      intensity: settings.defaultIntensity || 'medium',
      preserveFormat: true,
      addVariations: false
    };

    // Detect language if enabled
    let language = 'en';
    if (settings.autoDetectLanguage) {
      language = await detectLanguage(text);
    }

    // Process the text (using the same engine as the main app)
    const result = await processText(text, options, language);
    
    // Send result to content script for display
    chrome.tabs.sendMessage(tab.id, {
      action: 'showResult',
      data: {
        originalText: text,
        humanizedText: result.humanizedText,
        improvementScore: result.improvementScore,
        processingTime: result.processingTime,
        style: style,
        language: language
      }
    });

    // Track usage
    if (settings.trackUsage) {
      trackEvent('text_humanized', {
        style: style,
        language: language,
        textLength: text.length,
        improvementScore: result.improvementScore,
        source: 'extension',
        website: new URL(tab.url).hostname
      });
    }

    showNotification(`Text humanized! ${result.improvementScore}% improvement`, 'success');

  } catch (error) {
    console.error('Humanization failed:', error);
    showNotification('Failed to humanize text. Please try again.', 'error');
    
    trackEvent('humanization_error', {
      error: error.message,
      source: 'extension'
    });
  }
}

// Text processing function (simplified version of main app logic)
async function processText(text, options, language) {
  // This would normally call the main processing engine
  // For now, we'll simulate the processing
  const startTime = Date.now();
  
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
  
  // Mock result (in real implementation, this would use the actual processing engine)
  const improvementScore = Math.floor(75 + Math.random() * 20); // 75-95%
  
  return {
    humanizedText: `[Humanized] ${text}`, // Placeholder
    improvementScore: improvementScore,
    processingTime: Date.now() - startTime,
    variations: []
  };
}

// Simple language detection (placeholder)
async function detectLanguage(text) {
  // Vietnamese detection
  if (/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i.test(text)) {
    return 'vi';
  }
  
  // Chinese detection
  if (/[\u4e00-\u9fff]/.test(text)) {
    return 'zh';
  }
  
  return 'en';
}

// Share transformation function
function shareTransformation(originalText, tab) {
  const shareUrl = `https://ghostlayer.app?share=true&text=${encodeURIComponent(originalText)}`;
  chrome.tabs.create({ url: shareUrl });
  
  trackEvent('transformation_shared', {
    source: 'extension',
    website: new URL(tab.url).hostname
  });
}

// Notification system
function showNotification(message, type = 'info') {
  chrome.storage.sync.get(['showNotifications'], (result) => {
    if (result.showNotifications !== false) {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, {
            action: 'showNotification',
            message: message,
            type: type
          });
        }
      });
    }
  });
}

// Analytics tracking
function trackEvent(eventName, data) {
  chrome.storage.sync.get(['trackUsage'], (result) => {
    if (result.trackUsage !== false) {
      // In production, this would send to analytics service
      console.log('Extension Event:', eventName, data);
      
      // Store locally for now
      chrome.storage.local.get(['analytics'], (result) => {
        const analytics = result.analytics || [];
        analytics.push({
          event: eventName,
          data: data,
          timestamp: Date.now()
        });
        
        // Keep only last 1000 events
        if (analytics.length > 1000) {
          analytics.splice(0, analytics.length - 1000);
        }
        
        chrome.storage.local.set({ analytics: analytics });
      });
    }
  });
}

// Handle messages from content script and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'humanizeText':
      humanizeText(request.text, request.style || 'academic', sender.tab);
      sendResponse({ success: true });
      break;
      
    case 'getSettings':
      chrome.storage.sync.get(null, (settings) => {
        sendResponse(settings);
      });
      return true; // Keep message channel open for async response
      
    case 'updateSettings':
      chrome.storage.sync.set(request.settings, () => {
        sendResponse({ success: true });
      });
      return true;
      
    case 'getAnalytics':
      chrome.storage.local.get(['analytics'], (result) => {
        sendResponse(result.analytics || []);
      });
      return true;
      
    default:
      sendResponse({ error: 'Unknown action' });
  }
});
