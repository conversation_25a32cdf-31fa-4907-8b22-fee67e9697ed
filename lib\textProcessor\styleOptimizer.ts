import { ProcessingOptions } from '@/types';

export function optimizeStyle(text: string, options: ProcessingOptions): string {
  let optimizedText = text;
  
  // Apply style-specific optimizations
  switch (options.style) {
    case 'formal':
      optimizedText = applyFormalOptimizations(optimizedText);
      break;
    case 'casual':
      optimizedText = applyCasualOptimizations(optimizedText);
      break;
    case 'academic':
      optimizedText = applyAcademicOptimizations(optimizedText);
      break;
    case 'creative':
      optimizedText = applyCreativeOptimizations(optimizedText);
      break;
    case 'technical':
      optimizedText = applyTechnicalOptimizations(optimizedText);
      break;
    default:
      optimizedText = applyBalancedOptimizations(optimizedText);
      break;
  }
  
  // Apply general optimizations
  optimizedText = improveReadability(optimizedText);
  optimizedText = enhanceFlow(optimizedText);
  
  return optimizedText;
}

function applyFormalOptimizations(text: string): string {
  let result = text;
  
  // Use more formal vocabulary
  const formalReplacements: { [key: string]: string } = {
    'get': 'obtain',
    'help': 'assist',
    'start': 'commence',
    'end': 'conclude',
    'show': 'demonstrate',
    'tell': 'inform',
    'ask': 'inquire',
    'need': 'require',
    'want': 'desire',
    'think': 'believe',
    'feel': 'consider',
    'see': 'observe',
    'look': 'examine',
    'find': 'discover',
    'give': 'provide',
    'take': 'acquire',
    'make': 'create',
    'do': 'perform',
    'go': 'proceed',
    'come': 'arrive',
    'put': 'place',
    'keep': 'maintain',
    'let': 'permit',
    'try': 'attempt',
    'use': 'utilize',
    'work': 'function',
    'turn': 'convert',
    'move': 'relocate',
    'run': 'operate',
    'play': 'engage',
    'live': 'reside',
    'stay': 'remain',
    'leave': 'depart',
    'stop': 'cease',
    'begin': 'commence',
    'finish': 'complete'
  };
  
  Object.entries(formalReplacements).forEach(([informal, formal]) => {
    const regex = new RegExp(`\\b${informal}\\b`, 'gi');
    result = result.replace(regex, formal);
  });
  
  // Add formal sentence structures
  result = addFormalStructures(result);
  
  return result;
}

function applyCasualOptimizations(text: string): string {
  let result = text;
  
  // Use more casual vocabulary
  const casualReplacements: { [key: string]: string } = {
    'obtain': 'get',
    'assist': 'help',
    'commence': 'start',
    'conclude': 'end',
    'demonstrate': 'show',
    'inform': 'tell',
    'inquire': 'ask',
    'require': 'need',
    'desire': 'want',
    'believe': 'think',
    'consider': 'feel',
    'observe': 'see',
    'examine': 'look at',
    'discover': 'find',
    'provide': 'give',
    'acquire': 'get',
    'create': 'make',
    'perform': 'do',
    'proceed': 'go',
    'arrive': 'come',
    'place': 'put',
    'maintain': 'keep',
    'permit': 'let',
    'attempt': 'try',
    'utilize': 'use',
    'function': 'work',
    'convert': 'change',
    'relocate': 'move',
    'operate': 'run',
    'engage': 'play',
    'reside': 'live',
    'remain': 'stay',
    'depart': 'leave',
    'cease': 'stop'
  };
  
  Object.entries(casualReplacements).forEach(([formal, casual]) => {
    const regex = new RegExp(`\\b${formal}\\b`, 'gi');
    result = result.replace(regex, casual);
  });
  
  // Add casual connectors
  result = addCasualConnectors(result);
  
  return result;
}

function applyAcademicOptimizations(text: string): string {
  let result = text;
  
  // Add academic phrases and structures
  const academicPhrases = [
    'It is evident that',
    'Research indicates that',
    'Studies have demonstrated that',
    'Evidence suggests that',
    'Analysis reveals that',
    'Findings indicate that',
    'Data shows that',
    'Results demonstrate that',
    'Investigation reveals that',
    'Examination indicates that'
  ];
  
  // Add hedging language
  const hedgingWords = [
    'appears to',
    'seems to',
    'tends to',
    'suggests that',
    'indicates that',
    'implies that',
    'may',
    'might',
    'could',
    'potentially',
    'presumably',
    'arguably'
  ];
  
  // Occasionally add academic framing
  if (Math.random() < 0.4) {
    const phrase = academicPhrases[Math.floor(Math.random() * academicPhrases.length)];
    result = phrase + ' ' + result.toLowerCase();
  }
  
  // Add complex sentence structures
  result = addComplexStructures(result);
  
  return result;
}

function applyCreativeOptimizations(text: string): string {
  let result = text;
  
  // Use more expressive and varied vocabulary
  const creativeReplacements: { [key: string]: string[] } = {
    'said': ['whispered', 'declared', 'proclaimed', 'announced', 'revealed', 'confessed'],
    'walked': ['strolled', 'wandered', 'ambled', 'strode', 'meandered', 'sauntered'],
    'looked': ['gazed', 'peered', 'glanced', 'stared', 'observed', 'examined'],
    'big': ['enormous', 'massive', 'colossal', 'gigantic', 'immense', 'vast'],
    'small': ['tiny', 'minuscule', 'petite', 'compact', 'diminutive', 'microscopic'],
    'good': ['excellent', 'magnificent', 'outstanding', 'remarkable', 'exceptional', 'superb'],
    'bad': ['terrible', 'awful', 'dreadful', 'horrible', 'atrocious', 'appalling'],
    'happy': ['delighted', 'ecstatic', 'jubilant', 'elated', 'overjoyed', 'thrilled'],
    'sad': ['melancholy', 'sorrowful', 'dejected', 'despondent', 'heartbroken', 'mournful']
  };
  
  Object.entries(creativeReplacements).forEach(([word, replacements]) => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    if (Math.random() < 0.6) {
      const replacement = replacements[Math.floor(Math.random() * replacements.length)];
      result = result.replace(regex, replacement);
    }
  });
  
  // Add creative sentence starters
  result = addCreativeStarters(result);
  
  return result;
}

function applyTechnicalOptimizations(text: string): string {
  let result = text;

  // Enhanced technical vocabulary with multiple options for variety
  const technicalReplacements: { [key: string]: string[] } = {
    'use': ['implement', 'utilize', 'deploy', 'employ', 'execute'],
    'make': ['generate', 'create', 'produce', 'construct', 'develop'],
    'show': ['demonstrate', 'display', 'present', 'exhibit', 'illustrate'],
    'get': ['retrieve', 'obtain', 'acquire', 'fetch', 'extract'],
    'put': ['insert', 'place', 'position', 'deploy', 'install'],
    'take': ['extract', 'capture', 'acquire', 'obtain', 'retrieve'],
    'find': ['locate', 'identify', 'detect', 'discover', 'pinpoint'],
    'check': ['verify', 'validate', 'examine', 'inspect', 'assess'],
    'fix': ['resolve', 'correct', 'repair', 'rectify', 'address'],
    'change': ['modify', 'alter', 'adjust', 'update', 'configure'],
    'add': ['append', 'insert', 'incorporate', 'integrate', 'include'],
    'remove': ['delete', 'eliminate', 'extract', 'purge', 'exclude'],
    'start': ['initialize', 'launch', 'commence', 'activate', 'initiate'],
    'stop': ['terminate', 'halt', 'cease', 'deactivate', 'suspend'],
    'run': ['execute', 'operate', 'process', 'perform', 'function'],
    'work': ['function', 'operate', 'perform', 'execute', 'process'],
    'break': ['malfunction', 'fail', 'error', 'crash', 'interrupt'],
    'connect': ['interface', 'link', 'integrate', 'couple', 'bridge'],
    'send': ['transmit', 'transfer', 'deliver', 'dispatch', 'forward'],
    'receive': ['acquire', 'obtain', 'capture', 'accept', 'process'],
    'save': ['store', 'preserve', 'archive', 'retain', 'cache'],
    'load': ['retrieve', 'fetch', 'import', 'access', 'initialize'],
    'copy': ['duplicate', 'replicate', 'clone', 'mirror', 'reproduce'],
    'move': ['transfer', 'relocate', 'migrate', 'shift', 'transport'],
    'open': ['access', 'initialize', 'launch', 'activate', 'enable'],
    'close': ['terminate', 'deactivate', 'shutdown', 'finalize', 'conclude'],
    'build': ['construct', 'develop', 'assemble', 'compile', 'engineer'],
    'test': ['validate', 'verify', 'examine', 'assess', 'evaluate'],
    'handle': ['process', 'manage', 'control', 'manipulate', 'operate'],
    'setup': ['configure', 'initialize', 'establish', 'prepare', 'deploy'],
    'update': ['modify', 'revise', 'refresh', 'upgrade', 'synchronize'],
    'create': ['generate', 'develop', 'construct', 'build', 'establish'],
    'design': ['architect', 'engineer', 'structure', 'plan', 'configure'],
    'manage': ['control', 'oversee', 'coordinate', 'supervise', 'administer'],
    'analyze': ['examine', 'evaluate', 'assess', 'investigate', 'scrutinize'],
    'optimize': ['enhance', 'improve', 'refine', 'streamline', 'maximize'],
    'monitor': ['track', 'observe', 'supervise', 'watch', 'survey'],
    'maintain': ['preserve', 'sustain', 'uphold', 'service', 'support']
  };

  // Apply technical replacements with variety (60% chance for higher impact)
  Object.entries(technicalReplacements).forEach(([casual, technicalOptions]) => {
    if (Math.random() < 0.6) {
      const regex = new RegExp(`\\b${casual}\\b`, 'gi');
      const selectedTechnical = technicalOptions[Math.floor(Math.random() * technicalOptions.length)];
      result = result.replace(regex, selectedTechnical);
    }
  });

  // Add technical precision and transitions
  result = addTechnicalPrecision(result);
  result = addTechnicalTransitions(result);

  return result;
}

function applyBalancedOptimizations(text: string): string {
  let result = text;
  
  // Apply moderate improvements without being too formal or casual
  result = improveWordChoice(result);
  result = addVariety(result);
  
  return result;
}

function addFormalStructures(text: string): string {
  // Very rarely add formal transitional phrases, and only when contextually appropriate
  const formalTransitions = [
    'Furthermore,',
    'Moreover,',
    'Additionally,',
    'However,'
  ];

  // Only add formal transitions very sparingly and for longer content
  if (text.length > 50 && Math.random() < 0.05) {
    const transition = formalTransitions[Math.floor(Math.random() * formalTransitions.length)];
    // Only add if text doesn't already start with a transition
    if (!/^(Furthermore|Moreover|Additionally|However|Therefore|Consequently|Meanwhile|Similarly)/i.test(text.trim())) {
      return transition + ' ' + text.toLowerCase();
    }
  }

  return text;
}

function addCasualConnectors(text: string): string {
  // Replace formal connectors with casual ones
  return text
    .replace(/Furthermore,/g, 'Also,')
    .replace(/Moreover,/g, 'Plus,')
    .replace(/Additionally,/g, 'And,')
    .replace(/Consequently,/g, 'So,')
    .replace(/Nevertheless,/g, 'But,')
    .replace(/However,/g, 'Though,')
    .replace(/Therefore,/g, 'So,')
    .replace(/Subsequently,/g, 'Then,');
}

function addComplexStructures(text: string): string {
  // Add subordinate clauses and complex sentence structures
  const complexStarters = [
    'While it is true that',
    'Although one might argue that',
    'Despite the fact that',
    'Given that',
    'Considering that',
    'In light of the fact that',
    'Notwithstanding the evidence that',
    'Insofar as'
  ];
  
  if (Math.random() < 0.3) {
    const starter = complexStarters[Math.floor(Math.random() * complexStarters.length)];
    return starter + ' ' + text.toLowerCase();
  }
  
  return text;
}

function addCreativeStarters(text: string): string {
  const creativeStarters = [
    'Imagine if',
    'Picture this:',
    'Consider the possibility that',
    'What if',
    'Envision a world where',
    'Think about it:',
    'Here\'s the thing:',
    'The truth is,',
    'Believe it or not,',
    'Surprisingly,'
  ];
  
  if (Math.random() < 0.25) {
    const starter = creativeStarters[Math.floor(Math.random() * creativeStarters.length)];
    return starter + ' ' + text.toLowerCase();
  }
  
  return text;
}

function addTechnicalPrecision(text: string): string {
  // Add technical qualifiers and precision
  const technicalQualifiers = [
    'specifically',
    'precisely',
    'exactly',
    'systematically',
    'methodically',
    'efficiently',
    'optimally',
    'programmatically',
    'algorithmically',
    'computationally',
    'architecturally',
    'strategically'
  ];

  // Occasionally add technical qualifiers (increased chance)
  if (Math.random() < 0.5) {
    const qualifier = technicalQualifiers[Math.floor(Math.random() * technicalQualifiers.length)];
    return text.replace(/\b(implement|execute|process|generate|configure|optimize|analyze|monitor)\b/, `$1 ${qualifier}`);
  }

  return text;
}

function addTechnicalTransitions(text: string): string {
  // Technical-specific transition phrases
  const technicalTransitions = [
    'Subsequently',
    'Consequently',
    'Therefore',
    'As a result',
    'Furthermore',
    'Additionally',
    'In this context',
    'Following this approach',
    'Through this methodology',
    'Via this implementation',
    'Using this framework',
    'By leveraging this approach',
    'Through systematic analysis',
    'Based on these parameters',
    'Given these specifications',
    'Under these conditions',
    'Within this architecture',
    'Through this configuration'
  ];

  // Split into sentences and occasionally add technical transitions
  const sentences = text.split(/(?<=[.!?])\s+/).filter(s => s.trim().length > 0);

  if (sentences.length > 1 && Math.random() < 0.3) {
    const randomIndex = Math.floor(Math.random() * (sentences.length - 1)) + 1;
    const transition = technicalTransitions[Math.floor(Math.random() * technicalTransitions.length)];

    // Only add if sentence doesn't already start with a transition
    if (!/^(Subsequently|Consequently|Therefore|Furthermore|Additionally|Moreover|However)/i.test(sentences[randomIndex].trim())) {
      sentences[randomIndex] = transition + ', ' + sentences[randomIndex].toLowerCase();
    }
  }

  return sentences.join(' ');
}

function improveReadability(text: string): string {
  // Break up long sentences
  let result = text;
  
  // Split sentences that are too long (over 25 words)
  const sentences = result.split(/[.!?]+/);
  const improvedSentences = sentences.map(sentence => {
    const words = sentence.trim().split(/\s+/);
    if (words.length > 25) {
      // Find a good breaking point (conjunction or comma)
      const breakPoints = [', and', ', but', ', or', ', so', ', yet'];
      for (const breakPoint of breakPoints) {
        if (sentence.includes(breakPoint)) {
          return sentence.replace(breakPoint, '.' + breakPoint.substring(1));
        }
      }
    }
    return sentence;
  });
  
  return improvedSentences.join('.').replace(/\.\./g, '.');
}

function enhanceFlow(text: string): string {
  // Add transitional phrases to improve flow between sentences
  const transitions = [
    'In addition,',
    'Furthermore,',
    'Moreover,',
    'Similarly,',
    'On the other hand,',
    'However,',
    'Nevertheless,',
    'Consequently,',
    'As a result,',
    'Therefore,'
  ];
  
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  return sentences.map((sentence, index) => {
    if (index > 0 && Math.random() < 0.2) {
      const transition = transitions[Math.floor(Math.random() * transitions.length)];
      return transition + ' ' + sentence.trim().toLowerCase();
    }
    return sentence.trim();
  }).join('. ') + '.';
}

function improveWordChoice(text: string): string {
  // Replace overused words with better alternatives
  const improvements: { [key: string]: string[] } = {
    'thing': ['element', 'aspect', 'component', 'factor', 'item'],
    'stuff': ['material', 'content', 'items', 'elements', 'components'],
    'a lot': ['numerous', 'many', 'considerable', 'substantial', 'significant'],
    'really': ['genuinely', 'truly', 'actually', 'indeed', 'certainly'],
    'very': ['extremely', 'quite', 'remarkably', 'exceptionally', 'particularly']
  };
  
  let result = text;
  Object.entries(improvements).forEach(([overused, alternatives]) => {
    if (Math.random() < 0.5) {
      const alternative = alternatives[Math.floor(Math.random() * alternatives.length)];
      const regex = new RegExp(`\\b${overused}\\b`, 'gi');
      result = result.replace(regex, alternative);
    }
  });
  
  return result;
}

function addVariety(text: string): string {
  // Add sentence structure variety
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  return sentences.map((sentence, index) => {
    let result = sentence.trim();
    
    // Vary sentence beginnings
    if (index > 0 && Math.random() < 0.3) {
      const starters = [
        'Additionally,',
        'Furthermore,',
        'In contrast,',
        'Similarly,',
        'Meanwhile,',
        'Subsequently,'
      ];
      const starter = starters[Math.floor(Math.random() * starters.length)];
      result = starter + ' ' + result.toLowerCase();
    }
    
    return result;
  }).join('. ') + '.';
}