<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GhostLayer - AI Text Humanizer</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      width: 380px;
      min-height: 500px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
      color: white;
      font-size: 14px;
      line-height: 1.4;
    }

    .header {
      padding: 20px;
      text-align: center;
      background: rgba(255, 255, 255, 0.05);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .logo {
      font-size: 32px;
      margin-bottom: 8px;
    }

    .title {
      font-size: 18px;
      font-weight: 600;
      background: linear-gradient(45deg, #60a5fa, #a855f7);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 4px;
    }

    .subtitle {
      font-size: 12px;
      color: #94a3b8;
    }

    .main-content {
      padding: 20px;
    }

    .quick-actions {
      margin-bottom: 24px;
    }

    .section-title {
      font-size: 13px;
      font-weight: 600;
      color: #e2e8f0;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .btn {
      width: 100%;
      padding: 12px 16px;
      border: none;
      border-radius: 8px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-bottom: 8px;
    }

    .btn-primary {
      background: linear-gradient(45deg, #3b82f6, #8b5cf6);
      color: white;
    }

    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    .btn-secondary {
      background: rgba(255, 255, 255, 0.1);
      color: #e2e8f0;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .text-area {
      width: 100%;
      min-height: 80px;
      padding: 12px;
      background: rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      color: white;
      font-size: 13px;
      resize: vertical;
      margin-bottom: 12px;
    }

    .text-area::placeholder {
      color: #64748b;
    }

    .style-selector {
      margin-bottom: 16px;
    }

    .style-selector label {
      display: block;
      margin-bottom: 6px;
      font-size: 12px;
      color: #94a3b8;
      font-weight: 500;
    }

    .style-selector select {
      width: 100%;
      padding: 8px 12px;
      background: rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      color: white;
      font-size: 13px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-bottom: 20px;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 12px;
      text-align: center;
    }

    .stat-value {
      font-size: 20px;
      font-weight: 600;
      color: #60a5fa;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 11px;
      color: #94a3b8;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .settings-section {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .setting-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }

    .setting-label {
      font-size: 13px;
      color: #e2e8f0;
    }

    .toggle {
      position: relative;
      width: 44px;
      height: 24px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      cursor: pointer;
      transition: background 0.2s;
    }

    .toggle.active {
      background: #3b82f6;
    }

    .toggle::after {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 50%;
      transition: transform 0.2s;
    }

    .toggle.active::after {
      transform: translateX(20px);
    }

    .footer {
      padding: 16px 20px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      background: rgba(0, 0, 0, 0.2);
      text-align: center;
    }

    .footer-links {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-bottom: 8px;
    }

    .footer-link {
      color: #94a3b8;
      text-decoration: none;
      font-size: 12px;
      transition: color 0.2s;
    }

    .footer-link:hover {
      color: #60a5fa;
    }

    .version {
      font-size: 11px;
      color: #64748b;
    }

    .loading {
      opacity: 0.6;
      pointer-events: none;
    }

    .result-preview {
      background: rgba(34, 197, 94, 0.1);
      border: 1px solid rgba(34, 197, 94, 0.3);
      border-radius: 8px;
      padding: 12px;
      margin-top: 12px;
      display: none;
    }

    .result-preview.show {
      display: block;
    }

    .result-text {
      font-size: 13px;
      line-height: 1.4;
      color: #e2e8f0;
      margin-bottom: 8px;
    }

    .result-stats {
      display: flex;
      justify-content: space-between;
      font-size: 11px;
      color: #86efac;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">👻</div>
    <div class="title">GhostLayer</div>
    <div class="subtitle">AI Text Humanizer</div>
  </div>

  <div class="main-content">
    <!-- Quick Actions -->
    <div class="quick-actions">
      <div class="section-title">
        ⚡ Quick Actions
      </div>
      <button class="btn btn-primary" id="humanize-selection">
        🤖➡️👤 Humanize Selection
      </button>
      <button class="btn btn-secondary" id="paste-and-humanize">
        📋 Paste & Humanize
      </button>
      <button class="btn btn-secondary" id="open-website">
        🌐 Open GhostLayer
      </button>
    </div>

    <!-- Text Input -->
    <div class="text-input-section">
      <div class="section-title">
        ✍️ Manual Input
      </div>
      <textarea 
        class="text-area" 
        id="text-input" 
        placeholder="Paste your AI-generated text here to humanize it..."
      ></textarea>
      
      <div class="style-selector">
        <label>Writing Style:</label>
        <select id="style-select">
          <option value="academic">📚 Academic</option>
          <option value="professional">💼 Professional</option>
          <option value="creative">🎨 Creative</option>
          <option value="technical">⚙️ Technical</option>
        </select>
      </div>
      
      <button class="btn btn-primary" id="humanize-input">
        ✨ Humanize Text
      </button>
      
      <div class="result-preview" id="result-preview">
        <div class="result-text" id="result-text"></div>
        <div class="result-stats">
          <span id="improvement-score"></span>
          <span id="processing-time"></span>
        </div>
      </div>
    </div>

    <!-- Stats -->
    <div class="stats-section">
      <div class="section-title">
        📊 Your Stats
      </div>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-value" id="today-count">0</div>
          <div class="stat-label">Today</div>
        </div>
        <div class="stat-card">
          <div class="stat-value" id="total-count">0</div>
          <div class="stat-label">Total</div>
        </div>
      </div>
    </div>

    <!-- Settings -->
    <div class="settings-section">
      <div class="section-title">
        ⚙️ Settings
      </div>
      <div class="setting-item">
        <span class="setting-label">Auto-detect Language</span>
        <div class="toggle" id="auto-detect-toggle"></div>
      </div>
      <div class="setting-item">
        <span class="setting-label">Show Notifications</span>
        <div class="toggle" id="notifications-toggle"></div>
      </div>
      <div class="setting-item">
        <span class="setting-label">Usage Analytics</span>
        <div class="toggle" id="analytics-toggle"></div>
      </div>
    </div>
  </div>

  <div class="footer">
    <div class="footer-links">
      <a href="#" class="footer-link" id="help-link">Help</a>
      <a href="#" class="footer-link" id="feedback-link">Feedback</a>
      <a href="#" class="footer-link" id="privacy-link">Privacy</a>
    </div>
    <div class="version">v1.0.0</div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
