Please implement the following two critical improvements to the GhostLayer text humanization system:

1. **Preserve Capitalization of Abbreviations and Acronyms**:
   - Fix the text processing algorithm to maintain the original capitalization of abbreviations, acronyms, and proper nouns
   - Ensure that terms like "AI", "LLM", "API", "URL", "HTML", "CSS", "JavaScript", "GitHub", "GIGO", etc. remain capitalized in the output
   - The humanization process should NOT convert "AI" to "ai", "LLM" to "llm", or similar transformations
   - Implement a whitelist or pattern recognition system to identify and preserve common technical abbreviations, company names, and proper nouns
   - Test with various inputs containing mixed-case abbreviations to ensure consistent preservation
   - This fix should be applied to the core text processing logic in the `lib/textProcessor` directory

2. **Implement Word Embeddings for Enhanced Text Humanization**:
   - Integrate word embedding technology (such as Word2Vec, GloVe, or similar) to improve the semantic understanding during text humanization
   - Use word embeddings to find more contextually appropriate synonyms and word replacements that maintain semantic meaning
   - Implement embedding-based similarity scoring to ensure replacement words have similar semantic meaning to the original words
   - Consider using pre-trained embeddings or training custom embeddings on human-written text to improve the naturalness of output
   - The word embeddings should enhance the existing humanization algorithms rather than replace them entirely
   - Ensure the embedding-based improvements work across all writing styles (balanced, formal, casual, academic, creative, technical)
   - Maintain the current processing speed performance while adding these enhancements

Both improvements should be thoroughly tested to ensure they work correctly with the existing variation generation feature and maintain compatibility with all current functionality.