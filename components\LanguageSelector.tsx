'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Globe, Zap, CheckCircle, AlertCircle } from 'lucide-react';
import { detectLanguage } from '@/lib/languageDetection';
import { analytics } from '@/lib/analytics';

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  supported: boolean;
  quality: 'excellent' | 'good' | 'beta';
}

interface LanguageSelectorProps {
  selectedLanguage: string;
  onLanguageChange: (language: string) => void;
  inputText: string;
}

const SUPPORTED_LANGUAGES: Language[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    supported: true,
    quality: 'excellent'
  },
  {
    code: 'vi',
    name: 'Vietnamese',
    nativeName: 'Tiếng Việt',
    flag: '🇻🇳',
    supported: true,
    quality: 'excellent'
  },
  {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳',
    supported: true,
    quality: 'excellent'
  },
  {
    code: 'zh-tw',
    name: 'Chinese (Traditional)',
    nativeName: '繁體中文',
    flag: '🇹🇼',
    supported: true,
    quality: 'good'
  },
  {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
    supported: true,
    quality: 'excellent'
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    supported: true,
    quality: 'excellent'
  },
  {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
    supported: true,
    quality: 'good'
  },
  {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
    supported: false,
    quality: 'beta'
  },
  {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    flag: '🇰🇷',
    supported: false,
    quality: 'beta'
  }
];

export default function LanguageSelector({ selectedLanguage, onLanguageChange, inputText }: LanguageSelectorProps) {
  const [detectedLanguage, setDetectedLanguage] = useState<string | null>(null);
  const [isDetecting, setIsDetecting] = useState(false);
  const [autoDetectEnabled, setAutoDetectEnabled] = useState(true);

  // Auto-detect language when text changes
  useEffect(() => {
    if (inputText.trim().length > 20 && autoDetectEnabled) {
      setIsDetecting(true);
      
      // Debounce language detection
      const timer = setTimeout(async () => {
        try {
          const detected = await detectLanguage(inputText);
          setDetectedLanguage(detected);
          
          // Auto-select if different from current and supported
          const detectedLang = SUPPORTED_LANGUAGES.find(lang => lang.code === detected);
          if (detectedLang && detectedLang.supported && detected !== selectedLanguage) {
            onLanguageChange(detected);
            analytics.trackShare('language_auto_detected');
          }
        } catch (error) {
          console.warn('Language detection failed:', error);
        } finally {
          setIsDetecting(false);
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [inputText, autoDetectEnabled, selectedLanguage, onLanguageChange]);

  const handleLanguageSelect = (languageCode: string) => {
    const language = SUPPORTED_LANGUAGES.find(lang => lang.code === languageCode);
    if (language && language.supported) {
      onLanguageChange(languageCode);
      analytics.trackShare('language_manually_selected');
    }
  };

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'bg-green-500/20 text-green-400';
      case 'good': return 'bg-blue-500/20 text-blue-400';
      case 'beta': return 'bg-yellow-500/20 text-yellow-400';
      default: return 'bg-gray-500/20 text-gray-400';
    }
  };

  const selectedLang = SUPPORTED_LANGUAGES.find(lang => lang.code === selectedLanguage);
  const detectedLang = SUPPORTED_LANGUAGES.find(lang => lang.code === detectedLanguage);

  return (
    <Card className="p-4 bg-white/5 backdrop-blur-lg border-white/10">
      <div className="flex items-center gap-3 mb-4">
        <div className="p-2 bg-blue-500/20 rounded-lg">
          <Globe className="w-5 h-5 text-blue-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white">Language Settings</h3>
          <p className="text-gray-400 text-sm">Select your content language for optimal humanization</p>
        </div>
      </div>

      {/* Auto-detection status */}
      {isDetecting && (
        <div className="flex items-center gap-2 mb-4 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
          <span className="text-blue-400 text-sm">Detecting language...</span>
        </div>
      )}

      {detectedLanguage && detectedLang && !isDetecting && (
        <div className="flex items-center gap-2 mb-4 p-3 bg-green-500/10 rounded-lg border border-green-500/20">
          <CheckCircle className="w-4 h-4 text-green-400" />
          <span className="text-green-400 text-sm">
            Detected: {detectedLang.flag} {detectedLang.name}
            {detectedLanguage !== selectedLanguage && detectedLang.supported && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleLanguageSelect(detectedLanguage)}
                className="ml-2 text-green-400 hover:text-green-300"
              >
                Switch to {detectedLang.name}
              </Button>
            )}
          </span>
        </div>
      )}

      {/* Language selector */}
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium text-gray-300 mb-2 block">
            Content Language
          </label>
          <Select value={selectedLanguage} onValueChange={handleLanguageSelect}>
            <SelectTrigger className="w-full bg-slate-800/50 border-gray-600 text-white">
              <SelectValue>
                {selectedLang && (
                  <div className="flex items-center gap-2">
                    <span>{selectedLang.flag}</span>
                    <span>{selectedLang.name}</span>
                    <Badge className={getQualityColor(selectedLang.quality)}>
                      {selectedLang.quality}
                    </Badge>
                  </div>
                )}
              </SelectValue>
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-gray-600">
              {SUPPORTED_LANGUAGES.map((language) => (
                <SelectItem 
                  key={language.code} 
                  value={language.code}
                  disabled={!language.supported}
                  className="text-white hover:bg-slate-700"
                >
                  <div className="flex items-center gap-3 w-full">
                    <span className="text-lg">{language.flag}</span>
                    <div className="flex-1">
                      <div className="font-medium">{language.name}</div>
                      <div className="text-xs text-gray-400">{language.nativeName}</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getQualityColor(language.quality)}>
                        {language.quality}
                      </Badge>
                      {!language.supported && (
                        <Badge variant="secondary" className="bg-gray-500/20 text-gray-400">
                          Coming Soon
                        </Badge>
                      )}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Auto-detect toggle */}
        <div className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg">
          <div>
            <div className="text-sm font-medium text-white">Auto-detect Language</div>
            <div className="text-xs text-gray-400">Automatically detect and switch language based on your text</div>
          </div>
          <Button
            size="sm"
            variant={autoDetectEnabled ? "default" : "outline"}
            onClick={() => setAutoDetectEnabled(!autoDetectEnabled)}
            className={autoDetectEnabled ? "bg-blue-600 hover:bg-blue-700" : "border-gray-600 text-gray-300"}
          >
            <Zap className="w-4 h-4 mr-1" />
            {autoDetectEnabled ? 'On' : 'Off'}
          </Button>
        </div>

        {/* Language-specific features */}
        {selectedLang && selectedLang.supported && (
          <div className="p-3 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg border border-purple-500/20">
            <h4 className="text-sm font-semibold text-purple-400 mb-2">
              {selectedLang.flag} {selectedLang.name} Features
            </h4>
            <div className="space-y-1 text-xs text-gray-300">
              {selectedLang.code === 'en' && (
                <>
                  <div>• Advanced synonym replacement (50,000+ words)</div>
                  <div>• Context-aware paraphrasing</div>
                  <div>• Academic & professional style optimization</div>
                </>
              )}
              {selectedLang.code === 'vi' && (
                <>
                  <div>• Vietnamese-specific synonym dictionary (2,000+ pairs)</div>
                  <div>• Cultural context awareness</div>
                  <div>• Formal/informal tone adjustment</div>
                </>
              )}
              {(selectedLang.code === 'zh' || selectedLang.code === 'zh-tw') && (
                <>
                  <div>• Chinese synonym dictionary (3,000+ pairs)</div>
                  <div>• Traditional/Simplified character support</div>
                  <div>• Cultural nuance preservation</div>
                </>
              )}
              {selectedLang.code === 'es' && (
                <>
                  <div>• Spanish synonym dictionary (2,500+ pairs)</div>
                  <div>• Regional variations (Latin America/Spain)</div>
                  <div>• Formal/informal register adjustment</div>
                </>
              )}
              {selectedLang.code === 'fr' && (
                <>
                  <div>• French synonym dictionary (2,200+ pairs)</div>
                  <div>• Formal language patterns (vous/tu)</div>
                  <div>• Academic and business French</div>
                </>
              )}
              {selectedLang.code === 'ja' && (
                <>
                  <div>• Japanese synonym dictionary (1,800+ pairs)</div>
                  <div>• Keigo (honorific language) support</div>
                  <div>• Hiragana/Katakana/Kanji optimization</div>
                </>
              )}
            </div>
          </div>
        )}

        {/* Unsupported language notice */}
        {selectedLang && !selectedLang.supported && (
          <div className="flex items-center gap-2 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
            <AlertCircle className="w-4 h-4 text-yellow-400" />
            <div className="text-yellow-400 text-sm">
              {selectedLang.name} support is coming soon! Currently processing with English algorithms.
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}
