'use client';

import { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Lightbulb, 
  Wand2, 
  TrendingUp, 
  Target, 
  Sparkles,
  CheckCircle,
  X,
  RefreshCw,
  Brain,
  Zap,
  Eye,
  MessageSquare,
  Heart,
  Share2,
  Clock,
  Award
} from 'lucide-react';
import { ProcessingResult } from '@/types';
import { analytics } from '@/lib/analytics';

interface AISuggestion {
  id: string;
  type: 'improvement' | 'style' | 'engagement' | 'seo' | 'clarity';
  title: string;
  description: string;
  originalText: string;
  suggestedText: string;
  impact: 'low' | 'medium' | 'high';
  confidence: number;
  reasoning: string;
  category: string;
}

interface AIContentSuggestionsProps {
  text: string;
  result: ProcessingResult | null;
  onApplySuggestion: (suggestion: AISuggestion) => void;
  onTextUpdate: (newText: string) => void;
}

export default function AIContentSuggestions({ text, result, onApplySuggestion, onTextUpdate }: AIContentSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [appliedSuggestions, setAppliedSuggestions] = useState<Set<string>>(new Set());
  const [autoImproveEnabled, setAutoImproveEnabled] = useState(false);
  const [realTimeMode, setRealTimeMode] = useState(false);
  const [improvementScore, setImprovementScore] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Generate AI suggestions based on the text
  const generateSuggestions = async () => {
    if (!text || text.trim().length < 20) return;
    
    setIsGenerating(true);
    try {
      // Simulate AI suggestion generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockSuggestions: AISuggestion[] = [
        {
          id: '1',
          type: 'engagement',
          title: 'Add Emotional Hook',
          description: 'Make the opening more engaging with an emotional connection',
          originalText: text.substring(0, 50) + '...',
          suggestedText: 'Imagine discovering a tool that transforms your writing instantly...',
          impact: 'high',
          confidence: 92,
          reasoning: 'Starting with "Imagine" creates immediate reader engagement and curiosity',
          category: 'Opening Enhancement'
        },
        {
          id: '2',
          type: 'clarity',
          title: 'Simplify Complex Sentence',
          description: 'Break down a complex sentence for better readability',
          originalText: 'The implementation of artificial intelligence in modern business processes...',
          suggestedText: 'AI is changing how businesses work. Modern companies use smart technology...',
          impact: 'medium',
          confidence: 88,
          reasoning: 'Shorter sentences improve readability and comprehension',
          category: 'Clarity Improvement'
        },
        {
          id: '3',
          type: 'seo',
          title: 'Add Target Keywords',
          description: 'Include relevant keywords naturally in the content',
          originalText: 'This tool helps with writing',
          suggestedText: 'This AI text humanization tool helps with content writing and SEO optimization',
          impact: 'high',
          confidence: 85,
          reasoning: 'Including target keywords improves search engine visibility',
          category: 'SEO Enhancement'
        },
        {
          id: '4',
          type: 'style',
          title: 'Active Voice Conversion',
          description: 'Convert passive voice to active for more dynamic writing',
          originalText: 'The results were analyzed by the team',
          suggestedText: 'The team analyzed the results',
          impact: 'medium',
          confidence: 95,
          reasoning: 'Active voice creates more direct and engaging content',
          category: 'Style Enhancement'
        },
        {
          id: '5',
          type: 'improvement',
          title: 'Add Call-to-Action',
          description: 'Include a compelling call-to-action to drive engagement',
          originalText: 'This concludes our analysis.',
          suggestedText: 'Ready to transform your content? Try GhostLayer today and see the difference!',
          impact: 'high',
          confidence: 90,
          reasoning: 'Strong CTAs increase user engagement and conversion rates',
          category: 'Conversion Optimization'
        }
      ];
      
      setSuggestions(mockSuggestions);
      setImprovementScore(Math.floor(75 + Math.random() * 20));
      analytics.trackShare('ai_suggestions_generated');
      
    } catch (error) {
      console.error('Failed to generate suggestions:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Apply a suggestion to the text
  const applySuggestion = (suggestion: AISuggestion) => {
    const newApplied = new Set(appliedSuggestions);
    newApplied.add(suggestion.id);
    setAppliedSuggestions(newApplied);
    
    // Update the text with the suggestion
    const updatedText = text.replace(suggestion.originalText, suggestion.suggestedText);
    onTextUpdate(updatedText);
    onApplySuggestion(suggestion);
    
    analytics.trackShare('ai_suggestion_applied');
  };

  // Dismiss a suggestion
  const dismissSuggestion = (suggestionId: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
    analytics.trackShare('ai_suggestion_dismissed');
  };

  // Auto-apply high-confidence suggestions
  const autoApplyHighConfidenceSuggestions = () => {
    const highConfidenceSuggestions = suggestions.filter(
      s => s.confidence >= 90 && s.impact === 'high' && !appliedSuggestions.has(s.id)
    );
    
    highConfidenceSuggestions.forEach(suggestion => {
      applySuggestion(suggestion);
    });
    
    if (highConfidenceSuggestions.length > 0) {
      analytics.trackShare('auto_suggestions_applied');
    }
  };

  // Real-time suggestions as user types
  useEffect(() => {
    if (realTimeMode && text.length > 50) {
      const timer = setTimeout(() => {
        generateSuggestions();
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [text, realTimeMode]);

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-400 bg-red-500/20';
      case 'medium': return 'text-yellow-400 bg-yellow-500/20';
      case 'low': return 'text-green-400 bg-green-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'engagement': return <Heart className="w-4 h-4" />;
      case 'clarity': return <Eye className="w-4 h-4" />;
      case 'seo': return <TrendingUp className="w-4 h-4" />;
      case 'style': return <Wand2 className="w-4 h-4" />;
      case 'improvement': return <Sparkles className="w-4 h-4" />;
      default: return <Lightbulb className="w-4 h-4" />;
    }
  };

  return (
    <Card className="bg-white/5 backdrop-blur-lg border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-white">
          <div className="p-2 bg-yellow-500/20 rounded-lg">
            <Brain className="w-5 h-5 text-yellow-400" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold">AI Content Suggestions</h3>
            <p className="text-gray-400 text-sm font-normal">Intelligent recommendations to improve your content</p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant={realTimeMode ? "default" : "outline"}
              onClick={() => setRealTimeMode(!realTimeMode)}
              className={realTimeMode ? "bg-blue-600 hover:bg-blue-700" : "border-gray-600 text-gray-300"}
            >
              <Zap className="w-4 h-4 mr-1" />
              Real-time
            </Button>
            <Button
              onClick={generateSuggestions}
              disabled={isGenerating || !text}
              className="bg-yellow-600 hover:bg-yellow-700"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Analyzing...
                </>
              ) : (
                <>
                  <Lightbulb className="w-4 h-4 mr-2" />
                  Get Suggestions
                </>
              )}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        {/* Quick Stats */}
        {suggestions.length > 0 && (
          <div className="grid grid-cols-4 gap-4 mb-6">
            <div className="p-3 bg-white/5 rounded-lg text-center">
              <div className="text-2xl font-bold text-yellow-400">{suggestions.length}</div>
              <div className="text-gray-400 text-sm">Suggestions</div>
            </div>
            <div className="p-3 bg-white/5 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-400">{appliedSuggestions.size}</div>
              <div className="text-gray-400 text-sm">Applied</div>
            </div>
            <div className="p-3 bg-white/5 rounded-lg text-center">
              <div className="text-2xl font-bold text-blue-400">{improvementScore}%</div>
              <div className="text-gray-400 text-sm">Improvement</div>
            </div>
            <div className="p-3 bg-white/5 rounded-lg text-center">
              <div className="text-2xl font-bold text-purple-400">
                {Math.round(suggestions.reduce((acc, s) => acc + s.confidence, 0) / suggestions.length)}%
              </div>
              <div className="text-gray-400 text-sm">Confidence</div>
            </div>
          </div>
        )}

        {/* Auto-improve toggle */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg border border-purple-500/20 mb-6">
          <div>
            <div className="text-white font-medium">Auto-Improve Mode</div>
            <div className="text-gray-400 text-sm">Automatically apply high-confidence suggestions</div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              onClick={autoApplyHighConfidenceSuggestions}
              disabled={suggestions.filter(s => s.confidence >= 90 && s.impact === 'high' && !appliedSuggestions.has(s.id)).length === 0}
              className="bg-purple-600 hover:bg-purple-700"
            >
              <Wand2 className="w-4 h-4 mr-1" />
              Auto-Apply
            </Button>
            <Button
              size="sm"
              variant={autoImproveEnabled ? "default" : "outline"}
              onClick={() => setAutoImproveEnabled(!autoImproveEnabled)}
              className={autoImproveEnabled ? "bg-blue-600 hover:bg-blue-700" : "border-gray-600 text-gray-300"}
            >
              {autoImproveEnabled ? 'On' : 'Off'}
            </Button>
          </div>
        </div>

        {/* Suggestions List */}
        <div className="space-y-4">
          {suggestions.length === 0 && !isGenerating && (
            <div className="text-center py-8">
              <Lightbulb className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-400 mb-3">No suggestions yet</p>
              <p className="text-gray-500 text-sm">Add some text and click "Get Suggestions" to see AI recommendations</p>
            </div>
          )}

          {suggestions.map((suggestion) => {
            const isApplied = appliedSuggestions.has(suggestion.id);
            
            return (
              <Card key={suggestion.id} className={`bg-white/5 border-white/10 ${isApplied ? 'opacity-60' : ''}`}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${getImpactColor(suggestion.impact)}`}>
                        {getTypeIcon(suggestion.type)}
                      </div>
                      <div>
                        <h4 className="text-white font-medium">{suggestion.title}</h4>
                        <p className="text-gray-400 text-sm">{suggestion.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getImpactColor(suggestion.impact)}>
                        {suggestion.impact} impact
                      </Badge>
                      <Badge className="bg-blue-500/20 text-blue-400">
                        {suggestion.confidence}% confidence
                      </Badge>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="p-3 bg-red-500/10 rounded-lg border border-red-500/20">
                      <div className="text-red-400 text-xs font-semibold mb-1">BEFORE</div>
                      <p className="text-gray-300 text-sm">{suggestion.originalText}</p>
                    </div>
                    <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                      <div className="text-green-400 text-xs font-semibold mb-1">AFTER</div>
                      <p className="text-gray-300 text-sm">{suggestion.suggestedText}</p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="bg-gray-500/20 text-gray-400">
                        {suggestion.category}
                      </Badge>
                      <span className="text-gray-500 text-xs">{suggestion.reasoning}</span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {isApplied ? (
                        <Badge className="bg-green-500/20 text-green-400">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Applied
                        </Badge>
                      ) : (
                        <>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => dismissSuggestion(suggestion.id)}
                            className="text-gray-400 hover:text-red-400"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => applySuggestion(suggestion)}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Apply
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Suggestion Categories */}
        {suggestions.length > 0 && (
          <div className="mt-6 p-4 bg-white/5 rounded-lg">
            <h5 className="text-white font-medium mb-3">Suggestion Categories</h5>
            <div className="flex flex-wrap gap-2">
              {Array.from(new Set(suggestions.map(s => s.category))).map((category) => {
                const count = suggestions.filter(s => s.category === category).length;
                return (
                  <Badge key={category} variant="secondary" className="bg-blue-500/20 text-blue-400">
                    {category} ({count})
                  </Badge>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
