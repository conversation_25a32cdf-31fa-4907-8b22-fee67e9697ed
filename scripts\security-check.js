#!/usr/bin/env node

/**
 * Security Check Script for <PERSON><PERSON>ayer
 * Validates security configurations and tests security measures
 */

const fs = require('fs');
const path = require('path');

console.log('🔒 GhostLayer Security Check\n');

// Check 1: Verify netlify.toml security headers
function checkNetlifyConfig() {
  console.log('1. Checking Netlify configuration...');
  
  const netlifyPath = path.join(__dirname, '..', 'netlify.toml');
  
  if (!fs.existsSync(netlifyPath)) {
    console.log('   ❌ netlify.toml not found');
    return false;
  }
  
  const content = fs.readFileSync(netlifyPath, 'utf8');
  
  const requiredHeaders = [
    'Strict-Transport-Security',
    'Content-Security-Policy',
    'X-Content-Type-Options',
    'X-Frame-Options',
    'X-XSS-Protection',
    'Referrer-Policy',
    'Permissions-Policy'
  ];
  
  let allPresent = true;
  requiredHeaders.forEach(header => {
    if (!content.includes(header)) {
      console.log(`   ❌ Missing security header: ${header}`);
      allPresent = false;
    }
  });
  
  if (allPresent) {
    console.log('   ✅ All required security headers present');
  }
  
  return allPresent;
}

// Check 2: Verify Next.js security configuration
function checkNextConfig() {
  console.log('\n2. Checking Next.js configuration...');
  
  const nextConfigPath = path.join(__dirname, '..', 'next.config.js');
  
  if (!fs.existsSync(nextConfigPath)) {
    console.log('   ❌ next.config.js not found');
    return false;
  }
  
  const content = fs.readFileSync(nextConfigPath, 'utf8');
  
  const securityFeatures = [
    'poweredByHeader: false',
    'compress: true',
    'removeConsole',
    'dangerouslyAllowSVG: false'
  ];
  
  let allPresent = true;
  securityFeatures.forEach(feature => {
    if (!content.includes(feature)) {
      console.log(`   ❌ Missing security feature: ${feature}`);
      allPresent = false;
    }
  });
  
  if (allPresent) {
    console.log('   ✅ Next.js security features configured');
  }
  
  return allPresent;
}

// Check 3: Verify security utilities exist
function checkSecurityUtils() {
  console.log('\n3. Checking security utilities...');
  
  const securityPath = path.join(__dirname, '..', 'lib', 'security.ts');
  
  if (!fs.existsSync(securityPath)) {
    console.log('   ❌ lib/security.ts not found');
    return false;
  }
  
  const content = fs.readFileSync(securityPath, 'utf8');
  
  const requiredFunctions = [
    'sanitizeInput',
    'validateTextContent',
    'checkRateLimit',
    'generateSecureToken',
    'initializeSecurity'
  ];
  
  let allPresent = true;
  requiredFunctions.forEach(func => {
    if (!content.includes(`export function ${func}`) && !content.includes(`${func}:`)) {
      console.log(`   ❌ Missing security function: ${func}`);
      allPresent = false;
    }
  });
  
  if (allPresent) {
    console.log('   ✅ All security utilities present');
  }
  
  return allPresent;
}

// Check 4: Verify SecurityProvider component
function checkSecurityProvider() {
  console.log('\n4. Checking SecurityProvider component...');
  
  const providerPath = path.join(__dirname, '..', 'components', 'SecurityProvider.tsx');
  
  if (!fs.existsSync(providerPath)) {
    console.log('   ❌ components/SecurityProvider.tsx not found');
    return false;
  }
  
  const content = fs.readFileSync(providerPath, 'utf8');
  
  if (content.includes('initializeSecurity') && content.includes('useEffect')) {
    console.log('   ✅ SecurityProvider component configured');
    return true;
  } else {
    console.log('   ❌ SecurityProvider component not properly configured');
    return false;
  }
}

// Check 5: Verify package.json security
function checkPackageJson() {
  console.log('\n5. Checking package.json security...');
  
  const packagePath = path.join(__dirname, '..', 'package.json');
  
  if (!fs.existsSync(packagePath)) {
    console.log('   ❌ package.json not found');
    return false;
  }
  
  const content = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  // Check for security-related scripts
  const hasSecurityScript = content.scripts && (
    content.scripts['security-check'] || 
    content.scripts['audit']
  );
  
  if (hasSecurityScript) {
    console.log('   ✅ Security scripts available');
  } else {
    console.log('   ⚠️  Consider adding security check scripts');
  }
  
  return true;
}

// Check 6: Test input validation
function testInputValidation() {
  console.log('\n6. Testing input validation...');
  
  try {
    // This would normally require running the actual validation functions
    // For now, we'll just check if the validation patterns exist
    const securityPath = path.join(__dirname, '..', 'lib', 'security.ts');
    const content = fs.readFileSync(securityPath, 'utf8');
    
    const patterns = [
      'SCRIPT_PATTERN',
      'HTML_PATTERN',
      'SQL_PATTERN',
      'MAX_INPUT_LENGTH'
    ];
    
    let allPresent = true;
    patterns.forEach(pattern => {
      if (!content.includes(pattern)) {
        console.log(`   ❌ Missing validation pattern: ${pattern}`);
        allPresent = false;
      }
    });
    
    if (allPresent) {
      console.log('   ✅ Input validation patterns configured');
    }
    
    return allPresent;
  } catch (error) {
    console.log('   ❌ Error testing input validation:', error.message);
    return false;
  }
}

// Run all checks
async function runSecurityCheck() {
  const results = [
    checkNetlifyConfig(),
    checkNextConfig(),
    checkSecurityUtils(),
    checkSecurityProvider(),
    checkPackageJson(),
    testInputValidation()
  ];
  
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log(`\n📊 Security Check Results: ${passed}/${total} checks passed`);
  
  if (passed === total) {
    console.log('🎉 All security checks passed! Your application is ready for secure deployment.');
  } else {
    console.log('⚠️  Some security checks failed. Please review and fix the issues above.');
    process.exit(1);
  }
}

// Additional security recommendations
function showRecommendations() {
  console.log('\n💡 Additional Security Recommendations:');
  console.log('   • Enable Netlify built-in DDoS protection');
  console.log('   • Set up monitoring for CSP violations');
  console.log('   • Regularly update dependencies with npm audit');
  console.log('   • Consider implementing server-side rate limiting');
  console.log('   • Use environment variables for sensitive configuration');
  console.log('   • Enable Netlify form spam protection if using forms');
  console.log('   • Set up automated security scanning in CI/CD');
}

// Run the security check
runSecurityCheck().then(() => {
  showRecommendations();
}).catch(error => {
  console.error('Security check failed:', error);
  process.exit(1);
});
