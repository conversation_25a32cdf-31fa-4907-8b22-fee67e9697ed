<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GhostLayer - Offline</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
      color: white;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .container {
      text-align: center;
      max-width: 500px;
      width: 100%;
    }

    .logo {
      font-size: 64px;
      margin-bottom: 24px;
      animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }

    .title {
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 16px;
      background: linear-gradient(45deg, #60a5fa, #a855f7);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .subtitle {
      font-size: 18px;
      color: #94a3b8;
      margin-bottom: 32px;
      line-height: 1.6;
    }

    .offline-features {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 32px;
      backdrop-filter: blur(10px);
    }

    .features-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #e2e8f0;
    }

    .feature-list {
      list-style: none;
      text-align: left;
    }

    .feature-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-size: 14px;
      color: #cbd5e1;
    }

    .feature-icon {
      margin-right: 12px;
      font-size: 16px;
    }

    .actions {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 24px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
      text-decoration: none;
      display: inline-block;
    }

    .btn-primary {
      background: linear-gradient(45deg, #3b82f6, #8b5cf6);
      color: white;
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    }

    .btn-secondary {
      background: rgba(255, 255, 255, 0.1);
      color: #e2e8f0;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .status {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 14px;
      color: #94a3b8;
      margin-top: 24px;
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #ef4444;
      animation: pulse 2s infinite;
    }

    .status-indicator.online {
      background: #10b981;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .cached-content {
      background: rgba(34, 197, 94, 0.1);
      border: 1px solid rgba(34, 197, 94, 0.3);
      border-radius: 12px;
      padding: 16px;
      margin-top: 24px;
      display: none;
    }

    .cached-content.show {
      display: block;
    }

    .cached-title {
      font-size: 16px;
      font-weight: 600;
      color: #86efac;
      margin-bottom: 8px;
    }

    .cached-text {
      font-size: 14px;
      color: #d1fae5;
    }

    @media (max-width: 480px) {
      .logo {
        font-size: 48px;
      }
      
      .title {
        font-size: 24px;
      }
      
      .subtitle {
        font-size: 16px;
      }
      
      .offline-features {
        padding: 16px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="logo">👻</div>
    <h1 class="title">You're Offline</h1>
    <p class="subtitle">
      Don't worry! GhostLayer works offline too. 
      You can still humanize text using our cached processing engine.
    </p>

    <div class="offline-features">
      <h3 class="features-title">Available Offline Features</h3>
      <ul class="feature-list">
        <li class="feature-item">
          <span class="feature-icon">🤖➡️👤</span>
          Basic text humanization
        </li>
        <li class="feature-item">
          <span class="feature-icon">🌍</span>
          Multi-language support
        </li>
        <li class="feature-item">
          <span class="feature-icon">💾</span>
          Cached processing algorithms
        </li>
        <li class="feature-item">
          <span class="feature-icon">📊</span>
          Local usage statistics
        </li>
        <li class="feature-item">
          <span class="feature-icon">🔄</span>
          Auto-sync when online
        </li>
      </ul>
    </div>

    <div class="actions">
      <button class="btn btn-primary" onclick="tryAgain()">
        🔄 Try Again
      </button>
      <button class="btn btn-secondary" onclick="goOffline()">
        📱 Use Offline Mode
      </button>
    </div>

    <div class="cached-content" id="cachedContent">
      <div class="cached-title">✨ Cached Content Available</div>
      <div class="cached-text">
        Your previous work is saved and ready to use offline.
      </div>
    </div>

    <div class="status">
      <div class="status-indicator" id="statusIndicator"></div>
      <span id="statusText">Checking connection...</span>
    </div>
  </div>

  <script>
    // Check online status
    function updateStatus() {
      const indicator = document.getElementById('statusIndicator');
      const text = document.getElementById('statusText');
      
      if (navigator.onLine) {
        indicator.classList.add('online');
        text.textContent = 'Connection restored';
        
        // Auto-redirect after 2 seconds
        setTimeout(() => {
          window.location.href = '/';
        }, 2000);
      } else {
        indicator.classList.remove('online');
        text.textContent = 'No internet connection';
      }
    }

    // Try to reconnect
    function tryAgain() {
      updateStatus();
      if (navigator.onLine) {
        window.location.href = '/';
      } else {
        // Show a message that we're still offline
        const text = document.getElementById('statusText');
        text.textContent = 'Still offline - check your connection';
      }
    }

    // Go to offline mode
    function goOffline() {
      // Try to open the main app in offline mode
      window.location.href = '/?offline=true';
    }

    // Check for cached content
    function checkCachedContent() {
      // Check if there's cached content available
      if ('caches' in window) {
        caches.match('/').then(response => {
          if (response) {
            document.getElementById('cachedContent').classList.add('show');
          }
        });
      }
    }

    // Listen for online/offline events
    window.addEventListener('online', updateStatus);
    window.addEventListener('offline', updateStatus);

    // Initial status check
    updateStatus();
    checkCachedContent();

    // Periodic connection check
    setInterval(updateStatus, 5000);

    // Service worker registration check
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then(registration => {
        console.log('Service Worker is ready');
        
        // Listen for messages from service worker
        navigator.serviceWorker.addEventListener('message', event => {
          if (event.data.type === 'CACHE_UPDATED') {
            checkCachedContent();
          }
        });
      });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        tryAgain();
      } else if (e.key === 'Escape') {
        goOffline();
      }
    });
  </script>
</body>
</html>
