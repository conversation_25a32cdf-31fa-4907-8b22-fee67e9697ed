﻿import { ProcessingOptions } from '@/types';

// French synonym dictionary (~2,200 pairs for European market)
const FRENCH_SYNONYMS: { [key: string]: string[] } = {
  // Common verbs
  'Ãªtre': ['constituer', 'reprÃ©senter', 'demeurer', 'se trouver'],
  'avoir': ['possÃ©der', 'dÃ©tenir', 'disposer de', 'jouir de'],
  'faire': ['rÃ©aliser', 'effectuer', 'exÃ©cuter', 'accomplir'],
  'dire': ['exprimer', 'dÃ©clarer', 'affirmer', 'Ã©noncer'],
  'voir': ['observer', 'contempler', 'apercevoir', 'distinguer'],
  'donner': ['accorder', 'octroyer', 'fournir', 'procurer'],
  'savoir': ['connaÃ®tre', 'maÃ®triser', 'comprendre', 'saisir'],
  'vouloir': ['dÃ©sirer', 'souhaiter', 'aspirer Ã ', 'ambitionner'],
  'aller': ['se rendre', 'se diriger', 'partir', 'voyager'],
  'venir': ['arriver', 'parvenir', 'survenir', 'approcher'],

  // Common adjectives
  'bon': ['excellent', 'remarquable', 'formidable', 'exceptionnel'],
  'mauvais': ['mÃ©diocre', 'dÃ©faillant', 'inadÃ©quat', 'dÃ©plorable'],
  'grand': ['immense', 'gigantesque', 'colossal', 'considÃ©rable'],
  'petit': ['minuscule', 'rÃ©duit', 'modeste', 'limitÃ©'],
  'important': ['essentiel', 'fondamental', 'crucial', 'primordial'],
  'nouveau': ['rÃ©cent', 'moderne', 'contemporain', 'inÃ©dit'],
  'ancien': ['antique', 'traditionnel', 'historique', 'vÃ©tuste'],
  'rapide': ['vÃ©loce', 'prompt', 'expÃ©ditif', 'accÃ©lÃ©rÃ©'],
  'lent': ['tardif', 'paresseux', 'nonchalant', 'traÃ®nard'],
  'facile': ['simple', 'aisÃ©', 'Ã©lÃ©mentaire', 'accessible'],

  // Common nouns
  'personne': ['individu', 'Ãªtre humain', 'personnage', 'citoyen'],
  'chose': ['objet', 'Ã©lÃ©ment', 'article', 'item'],
  'temps': ['pÃ©riode', 'Ã©poque', 'moment', 'instant'],
  'lieu': ['endroit', 'emplacement', 'localisation', 'site'],
  'maniÃ¨re': ['faÃ§on', 'mÃ©thode', 'procÃ©dÃ©', 'technique'],
  'problÃ¨me': ['difficultÃ©', 'obstacle', 'embarras', 'contrariÃ©tÃ©'],
  'rÃ©sultat': ['consÃ©quence', 'effet', 'aboutissement', 'produit'],
  'raison': ['motif', 'cause', 'fondement', 'justification'],
  'objectif': ['but', 'finalitÃ©', 'intention', 'dessein'],
  'systÃ¨me': ['structure', 'organisation', 'mÃ©canisme', 'dispositif'],

  // Academic terms
  'recherche': ['Ã©tude', 'investigation', 'exploration', 'enquÃªte'],
  'analyse': ['examen', 'Ã©valuation', 'inspection', 'dÃ©cortication'],
  'Ã©valuation': ['apprÃ©ciation', 'estimation', 'jugement', 'cotation'],
  'conclusion': ['dÃ©duction', 'infÃ©rence', 'aboutissement', 'rÃ©solution'],
  'solution': ['rÃ©ponse', 'rÃ©solution', 'remÃ¨de', 'alternative'],
  'application': ['usage', 'emploi', 'utilisation', 'mise en Å“uvre'],
  'effet': ['impact', 'consÃ©quence', 'rÃ©sultat', 'rÃ©percussion'],
  'influence': ['ascendant', 'emprise', 'action', 'incidence'],
  'tendance': ['inclination', 'propension', 'orientation', 'penchant'],
  'dÃ©veloppement': ['Ã©volution', 'progrÃ¨s', 'croissance', 'expansion'],

  // Technology terms
  'technologie': ['technique', 'science appliquÃ©e', 'innovation', 'procÃ©dÃ©'],
  'infrastructure': ['structure', 'organisation', 'ensemble', 'rÃ©seau'],
  'donnÃ©es': ['informations', 'statistiques', 'chiffres', 'Ã©lÃ©ments'],
  'information': ['renseignement', 'donnÃ©e', 'dÃ©tail', 'contenu'],
  'programme': ['logiciel', 'application', 'outil', 'systÃ¨me'],
  'rÃ©seau': ['connexion', 'liaison', 'maillage', 'infrastructure'],
  'site web': ['site internet', 'portail', 'plateforme numÃ©rique', 'page web'],
  'ordinateur': ['machine', 'Ã©quipement', 'dispositif', 'terminal'],

  // Business terms
  'entreprise': ['sociÃ©tÃ©', 'compagnie', 'organisation', 'firme'],
  'client': ['consommateur', 'utilisateur', 'acheteur', 'clientÃ¨le'],
  'produit': ['article', 'marchandise', 'bien', 'manufacture'],
  'service': ['prestation', 'assistance', 'support', 'aide'],
  'marchÃ©': ['secteur', 'domaine commercial', 'place', 'dÃ©bouchÃ©'],
  'profit': ['bÃ©nÃ©fice', 'gain', 'rendement', 'rapport'],
  'investissement': ['placement', 'mise de fonds', 'capital', 'financement'],
  'gestion': ['administration', 'direction', 'management', 'pilotage'],

  // Transition words
  'cependant': ['nÃ©anmoins', 'toutefois', 'pourtant', 'malgrÃ© tout'],
  'par consÃ©quent': ['donc', 'ainsi', 'en consÃ©quence', 'de ce fait'],
  'en outre': ['de plus', 'Ã©galement', 'par ailleurs', 'qui plus est'],
  'notamment': ['particuliÃ¨rement', 'surtout', 'spÃ©cialement', 'principalement'],
  'par exemple': ['comme', 'tel que', 'Ã  l\'instar de', 'Ã  titre d\'exemple'],
  'enfin': ['finalement', 'pour finir', 'en dernier lieu', 'pour conclure'],
  'd\'abord': ['premiÃ¨rement', 'en premier lieu', 'tout d\'abord', 'initialement'],
  'ensuite': ['puis', 'aprÃ¨s', 'par la suite', 'secondement'],

  // Common expressions
  'trÃ¨s': ['extrÃªmement', 'particuliÃ¨rement', 'remarquablement', 'considÃ©rablement'],
  'beaucoup': ['Ã©normÃ©ment', 'abondamment', 'largement', 'grandement'],
  'peu': ['faiblement', 'lÃ©gÃ¨rement', 'modÃ©rÃ©ment', 'parcimonieusement'],
  'toujours': ['constamment', 'continuellement', 'perpÃ©tuellement', 'invariablement'],
  'jamais': ['en aucun cas', 'nullement', 'point', 'aucunement'],
  'souvent': ['frÃ©quemment', 'rÃ©guliÃ¨rement', 'habituellement', 'couramment'],
  'rarement': ['peu souvent', 'exceptionnellement', 'sporadiquement', 'occasionnellement'],
  'immÃ©diatement': ['instantanÃ©ment', 'aussitÃ´t', 'sur-le-champ', 'sans dÃ©lai']
};

// French formal patterns
const FRENCH_FORMAL_PATTERNS = {
  // Formal expressions
  'je': ['l\'auteur', 'le soussignÃ©', 'celui qui Ã©crit'],
  'tu': ['vous', 'votre personne', 'le lecteur'],
  'nous': ['les auteurs', 'les soussignÃ©s', 'l\'Ã©quipe'],
  
  // Politeness markers
  's\'il vous plaÃ®t': ['je vous prie', 'veuillez', 'ayez l\'amabilitÃ©'],
  'merci': ['je vous remercie', 'mes remerciements', 'ma gratitude'],
  'pardon': ['excusez-moi', 'je vous prie de m\'excuser', 'veuillez m\'excuser']
};

export function processFrenchText(text: string, options: ProcessingOptions): string {
  let result = text;
  
  // Get replacement intensity
  const replacementChance = getReplacementChance(options.intensity);
  
  // Apply French-specific synonym replacement
  result = applyFrenchSynonyms(result, replacementChance);
  
  // Apply cultural context adjustments
  if (options.style === 'academic' || options.style === 'formal') {
    result = applyFormalFrench(result);
  }
  
  // Apply French sentence structure optimization
  result = optimizeFrenchSentenceStructure(result, options);
  
  // Clean up spacing and punctuation
  result = cleanFrenchText(result);
  
  return result;
}

function getReplacementChance(intensity: string): number {
  switch (intensity) {
    case 'light': return 0.2;
    case 'medium': return 0.4;
    case 'aggressive': return 0.6;
    default: return 0.3;
  }
}

function applyFrenchSynonyms(text: string, replacementChance: number): string {
  let result = text;
  
  // Split into words while preserving punctuation
  const words = text.split(/(\s+|[.,!?;:Â«Â»])/);
  
  for (let i = 0; i < words.length; i++) {
    const word = words[i].toLowerCase().trim();
    
    if (FRENCH_SYNONYMS[word] && Math.random() < replacementChance) {
      const synonyms = FRENCH_SYNONYMS[word];
      const replacement = synonyms[Math.floor(Math.random() * synonyms.length)];
      
      // Preserve original capitalization
      if (words[i][0] === words[i][0].toUpperCase()) {
        words[i] = replacement.charAt(0).toUpperCase() + replacement.slice(1);
      } else {
        words[i] = replacement;
      }
    }
  }
  
  return words.join('');
}

function applyFormalFrench(text: string): string {
  let result = text;
  
  // Apply formal patterns
  Object.entries(FRENCH_FORMAL_PATTERNS).forEach(([informal, formalOptions]) => {
    const regex = new RegExp(`\\b${informal}\\b`, 'gi');
    if (regex.test(result)) {
      const formal = formalOptions[Math.floor(Math.random() * formalOptions.length)];
      result = result.replace(regex, formal);
    }
  });
  
  return result;
}

function optimizeFrenchSentenceStructure(text: string, options: ProcessingOptions): string {
  let result = text;
  
  // French sentence optimization
  const sentences = result.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  const optimizedSentences = sentences.map(sentence => {
    let optimized = sentence.trim();
    
    // Add French transition words for better flow
    if (options.style === 'academic') {
      optimized = addFrenchTransitions(optimized);
    }
    
    // Optimize for French natural flow
    optimized = optimizeFrenchWordOrder(optimized);
    
    return optimized;
  });
  
  return optimizedSentences.join('. ') + '.';
}

function addFrenchTransitions(sentence: string): string {
  const transitions = [
    'Notamment,', 'En outre,', 'Cependant,', 'Par consÃ©quent,', 
    'Par exemple,', 'En effet,', 'ConcrÃ¨tement,'
  ];
  
  // 30% chance to add transition
  if (Math.random() < 0.3 && !sentence.match(/^(Notamment|En outre|Cependant|Par consÃ©quent)/)) {
    const transition = transitions[Math.floor(Math.random() * transitions.length)];
    return `${transition} ${sentence.toLowerCase()}`;
  }
  
  return sentence;
}

function optimizeFrenchWordOrder(sentence: string): string {
  // French follows Subject-Verb-Object order with some flexibility
  let result = sentence;
  
  // Move time expressions to the beginning (French preference)
  const timePattern = /\b(aujourd\'hui|maintenant|actuellement|Ã  prÃ©sent|en ce moment|prÃ©sentement)\b/gi;
  const timeMatch = result.match(timePattern);
  
  if (timeMatch) {
    result = result.replace(timePattern, '');
    result = `${timeMatch[0]} ${result}`.trim();
  }
  
  return result;
}

function cleanFrenchText(text: string): string {
  let result = text;
  
  // Fix spacing around French punctuation
  result = result.replace(/\s+([.,!?;:])/g, '$1');
  result = result.replace(/([.,!?;:])\s*/g, '$1 ');
  
  // Handle French quotation marks
  result = result.replace(/\s*Â«\s*/g, 'Â« ');
  result = result.replace(/\s*Â»\s*/g, ' Â»');
  
  // Remove multiple spaces
  result = result.replace(/\s{2,}/g, ' ');
  
  // Ensure proper capitalization after French sentence endings
  result = result.replace(/([.!?])\s*([a-zÃ Ã¢Ã¤Ã©Ã¨ÃªÃ«Ã¯Ã®Ã´Ã¶Ã¹Ã»Ã¼Ã¿Ã§])/g, 
    (match, punct, letter) => `${punct} ${letter.toUpperCase()}`);
  
  return result.trim();
}

// Export for use in main text processor
export { FRENCH_SYNONYMS, FRENCH_FORMAL_PATTERNS };

