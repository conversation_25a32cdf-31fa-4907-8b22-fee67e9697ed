'use client';

import { useState, useMemo } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Copy, Download, RotateCcw, CheckCircle, Eye, EyeOff } from 'lucide-react';
import { ProcessingResult } from '@/types';
import { analytics } from '@/lib/analytics';
import SocialShare from '@/components/SocialShare';
import { diffWords } from 'diff';

interface OutputDisplayProps {
  originalText: string;
  result: ProcessingResult | null;
  isProcessing: boolean;
}

export default function OutputDisplay({ originalText, result, isProcessing }: OutputDisplayProps) {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [showHighlighting, setShowHighlighting] = useState(true);

  // Generate highlighted text using diff
  const highlightedText = useMemo(() => {
    if (!result?.humanizedText || !originalText || !showHighlighting) {
      return result?.humanizedText || '';
    }

    const diff = diffWords(originalText, result.humanizedText);
    return diff.map((part, index) => {
      if (part.added) {
        return (
          <span
            key={index}
            className="bg-green-500/20 text-green-300 px-1 rounded"
            title="Added/Modified text"
          >
            {part.value}
          </span>
        );
      } else if (part.removed) {
        return null; // Don't show removed parts in the output
      } else {
        return (
          <span key={index} className="text-gray-300">
            {part.value}
          </span>
        );
      }
    }).filter(Boolean);
  }, [originalText, result?.humanizedText, showHighlighting]);

  const handleCopy = (text: string, index: number) => {
    navigator.clipboard.writeText(text);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);

    // Track analytics
    if (index === 0) {
      analytics.trackCopy('main');
    } else {
      analytics.trackCopy('variation', index);
    }
  };

  const handleDownload = (text: string, filename: string) => {
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);

    // Track analytics
    if (filename.includes('humanized-text-1')) {
      analytics.trackDownload('main');
    } else {
      const match = filename.match(/humanized-text-(\d+)/);
      const variationIndex = match ? parseInt(match[1]) : 1;
      analytics.trackDownload('variation', variationIndex);
    }
  };

  if (isProcessing) {
    return (
      <Card className="p-8 bg-white/5 backdrop-blur-lg border-white/10">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold text-white mb-2">Processing Your Text</h3>
          <p className="text-gray-400">Applying advanced humanization algorithms...</p>
        </div>
      </Card>
    );
  }

  if (!result) {
    return (
      <Card className="p-8 bg-white/5 backdrop-blur-lg border-white/10">
        <div className="text-center text-gray-400">
          <RotateCcw className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-semibold mb-2">Ready to Process</h3>
          <p>Enter your AI-generated text above and click "Humanize Text" to begin.</p>
        </div>
      </Card>
    );
  }

  const variations = result.variations || [result.humanizedText];

  return (
    <Card className="p-6 bg-white/5 backdrop-blur-lg border-white/10">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white">Humanized Output</h3>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Label htmlFor="highlight-toggle" className="text-sm text-gray-300">
              Highlight Changes
            </Label>
            <Switch
              id="highlight-toggle"
              checked={showHighlighting}
              onCheckedChange={setShowHighlighting}
            />
            {showHighlighting ? (
              <Eye className="w-4 h-4 text-green-400" />
            ) : (
              <EyeOff className="w-4 h-4 text-gray-400" />
            )}
          </div>
          <div className="flex gap-2">
            <Badge variant="secondary" className="bg-green-500/20 text-green-400">
              {result.improvementScore}% More Human
            </Badge>
            <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
              {result.processingTime}ms
            </Badge>
            {variations.length > 1 && (
              <Badge variant="secondary" className="bg-purple-500/20 text-purple-400">
                {variations.length} Variations
              </Badge>
            )}
          </div>
        </div>
      </div>

      <Tabs defaultValue="result" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-slate-800/50">
          <TabsTrigger value="result" className="text-white">Humanized Text</TabsTrigger>
          <TabsTrigger value="comparison" className="text-white">Side by Side</TabsTrigger>
          <TabsTrigger value="share" className="text-white">Share Results</TabsTrigger>
        </TabsList>

        <TabsContent value="result" className="mt-6">
          {variations.length > 1 && (
            <div className="mb-6 p-4 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg border border-purple-500/20">
              <p className="text-purple-300 text-sm text-center">
                <strong>{variations.length} variations</strong> generated. Each variation offers a unique approach to humanizing your text.
              </p>
            </div>
          )}
          <div className="space-y-6">
            {variations.map((variation, index) => {
              // Define distinct colors for each variation
              const variationColors = [
                'bg-blue-500/10 border-blue-500/30', // Primary blue
                'bg-purple-500/10 border-purple-500/30', // Purple
                'bg-green-500/10 border-green-500/30', // Green
                'bg-orange-500/10 border-orange-500/30', // Orange
                'bg-pink-500/10 border-pink-500/30', // Pink
              ];

              const colorClass = variations.length > 1
                ? variationColors[index % variationColors.length]
                : 'bg-slate-800/30 border-slate-700';

              const headerColors = [
                'text-blue-400 bg-blue-500/20',
                'text-purple-400 bg-purple-500/20',
                'text-green-400 bg-green-500/20',
                'text-orange-400 bg-orange-500/20',
                'text-pink-400 bg-pink-500/20',
              ];

              const headerColorClass = variations.length > 1
                ? headerColors[index % headerColors.length]
                : 'text-gray-400 bg-slate-700/50';

              return (
                <div key={index} className={`rounded-xl p-6 border-2 ${colorClass} transition-all duration-300 hover:shadow-lg`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className={`px-3 py-1 rounded-full text-sm font-semibold ${headerColorClass}`}>
                      {variations.length > 1 ? `Variation ${index + 1}` : 'Humanized Text'}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleCopy(variation, index)}
                        className="text-gray-400 hover:text-white hover:bg-white/10 transition-colors"
                      >
                        {copiedIndex === index ? (
                          <CheckCircle className="w-4 h-4" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDownload(variation, `humanized-text-${index + 1}.txt`)}
                        className="text-gray-400 hover:text-white hover:bg-white/10 transition-colors"
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="bg-black/20 rounded-lg p-4 border border-white/10">
                    {index === 0 && showHighlighting ? (
                      <div className="text-white leading-relaxed whitespace-pre-wrap text-base">
                        {highlightedText}
                      </div>
                    ) : (
                      <p className="text-white leading-relaxed whitespace-pre-wrap text-base">{variation}</p>
                    )}
                  </div>
                  {variations.length > 1 && (
                    <div className="mt-3 text-xs text-gray-500 text-center">
                      Click to copy or download this variation
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </TabsContent>



        <TabsContent value="comparison" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-red-500/10 rounded-lg p-4 border border-red-500/20">
              <h4 className="text-sm font-semibold text-red-400 mb-3">Original (AI-Generated)</h4>
              <p className="text-gray-300 leading-relaxed whitespace-pre-wrap text-sm">{originalText}</p>
            </div>
            <div className="bg-green-500/10 rounded-lg p-4 border border-green-500/20">
              <h4 className="text-sm font-semibold text-green-400 mb-3 flex items-center gap-2">
                Humanized
                {showHighlighting && (
                  <Badge className="bg-green-500/20 text-green-400 text-xs">
                    Changes Highlighted
                  </Badge>
                )}
              </h4>
              {showHighlighting ? (
                <div className="text-gray-300 leading-relaxed whitespace-pre-wrap text-sm">
                  {highlightedText}
                </div>
              ) : (
                <p className="text-gray-300 leading-relaxed whitespace-pre-wrap text-sm">{result.humanizedText}</p>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="share" className="mt-6">
          <SocialShare
            originalText={originalText}
            result={result}
            onShare={(platform) => analytics.trackShare(platform)}
          />
        </TabsContent>
      </Tabs>
    </Card>
  );
}