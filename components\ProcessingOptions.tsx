'use client';

import { useSession } from 'next-auth/react';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Settings, Zap, Palette, Shield, Crown, Lock } from 'lucide-react';
import { analytics } from '@/lib/analytics';

interface ProcessingOptionsProps {
  options: {
    intensity: string;
    style: string;
    preserveFormat: boolean;
    addVariations: boolean;
  };
  onChange: (options: any) => void;
}

export default function ProcessingOptions({ options, onChange }: ProcessingOptionsProps) {
  const { data: session } = useSession();
  const isPremium = session?.user?.tier === 'premium' || session?.user?.tier === 'pro';
  const intensityValue = options.intensity === 'light' ? 25 : options.intensity === 'medium' ? 50 : 75;

  const handleIntensityChange = (value: number[]) => {
    const intensity = value[0] <= 33 ? 'light' : value[0] <= 66 ? 'medium' : 'heavy';

    // Restrict heavy mode to premium users
    if (intensity === 'heavy' && !isPremium) {
      return; // Don't allow change
    }

    onChange({ ...options, intensity });
  };

  return (
    <Card className="p-6 bg-white/5 backdrop-blur-lg border-white/10">
      <h3 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
        <Settings className="w-5 h-5" />
        Processing Options
      </h3>
      
      <div className="space-y-6">
        <div>
          <Label className="text-white mb-3 block flex items-center gap-2">
            <Zap className="w-4 h-4 text-yellow-400" />
            Transformation Intensity
          </Label>
          <div className="px-2">
            <Slider
              value={[intensityValue]}
              onValueChange={handleIntensityChange}
              max={isPremium ? 100 : 66}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-white mt-2">
              <span>Light</span>
              <span>Medium</span>
              <div className="flex items-center gap-1">
                <span className={!isPremium ? 'text-gray-500' : ''}>Heavy</span>
                {!isPremium && (
                  <div className="flex items-center gap-1">
                    <Lock className="w-3 h-3 text-yellow-400" />
                    <Badge className="bg-yellow-500/20 text-yellow-400 text-xs px-1 py-0">
                      <Crown className="w-2 h-2 mr-1" />
                      Premium
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </div>
          <p className="text-xs text-gray-400 mt-2">
            {options.intensity === 'light' && 'Minimal changes, preserves original structure'}
            {options.intensity === 'medium' && 'Balanced transformation with good readability'}
            {options.intensity === 'heavy' && 'Maximum humanization, significant restructuring'}
          </p>
          {!isPremium && (
            <div className="mt-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-400 text-sm">
                <Crown className="w-4 h-4" />
                <span className="font-medium">Upgrade to Premium</span>
              </div>
              <p className="text-xs text-gray-300 mt-1">
                Unlock Heavy intensity mode and advanced humanization features.
                <Button className="text-yellow-400 underline p-0 h-auto ml-1" variant="link">
                  Refer friends to earn credits!
                </Button>
              </p>
            </div>
          )}
        </div>

        <div>
          <Label className="text-white mb-3 block flex items-center gap-2">
            <Palette className="w-4 h-4 text-purple-400" />
            Writing Style
          </Label>
          <Select value={options.style} onValueChange={(value) => {
            analytics.trackStyleChange(options.style, value);
            onChange({ ...options, style: value });
          }}>
            <SelectTrigger className="bg-slate-800/50 border-slate-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700 text-white">
              <SelectItem value="academic" className="text-white hover:bg-blue-600 focus:bg-blue-600">Academic (Recommended)</SelectItem>
              <SelectItem value="balanced" className="text-white hover:bg-blue-600 focus:bg-blue-600">Balanced</SelectItem>
              <SelectItem value="formal" className="text-white hover:bg-blue-600 focus:bg-blue-600">Formal</SelectItem>
              <SelectItem value="casual" className="text-white hover:bg-blue-600 focus:bg-blue-600">Casual</SelectItem>
              <SelectItem value="creative" className="text-white hover:bg-blue-600 focus:bg-blue-600">Creative</SelectItem>
              <SelectItem value="technical" className="text-white hover:bg-blue-600 focus:bg-blue-600">Technical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-white flex items-center gap-2">
                <Shield className="w-4 h-4 text-green-400" />
                Preserve Formatting
              </Label>
              <p className="text-xs text-gray-400 mt-1">
                Keep original paragraph structure and formatting
              </p>
            </div>
            <Switch
              checked={options.preserveFormat}
              onCheckedChange={(checked) => onChange({ ...options, preserveFormat: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-white">Generate Variations</Label>
              <p className="text-xs text-gray-400 mt-1">
                Create multiple versions for comparison
              </p>
            </div>
            <Switch
              checked={options.addVariations}
              onCheckedChange={(checked) => {
                analytics.trackVariationsToggle(checked);
                onChange({ ...options, addVariations: checked });
              }}
            />
          </div>
        </div>
      </div>
    </Card>
  );
}