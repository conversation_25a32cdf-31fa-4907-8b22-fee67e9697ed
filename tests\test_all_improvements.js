// Test all four improvements
const fs = require('fs');
const path = require('path');

function testAllImprovements() {
  console.log('=== TESTING ALL FOUR IMPROVEMENTS ===\n');
  
  // Test cases for each improvement
  const testCases = {
    punctuation: [
      'Imagine two scenarios:.',
      'garbage in, garbage out. ',
      'This is a test:. with double punctuation'
    ],
    quotationMarks: [
      '"about"creating engaging posts"',
      'word"quote and quote"word',
      'text"without spaces"more text',
      'proper "spacing should" be maintained'
    ],
    capitalization: [
      'AI systems work with LLM technology',
      'The API connects to GitHub and LinkedIn',
      'Microsoft Azure and AWS provide cloud services',
      'This sentence starts with Capital letters'
    ],
    technical: [
      'We need to use the system to make better results',
      'The program will run and check the data',
      'Users can get information and save files',
      'The application will start and connect to servers'
    ]
  };
  
  console.log('1. TESTING PUNCTUATION FIXES');
  console.log('=' .repeat(40));
  testCases.punctuation.forEach((test, index) => {
    const fixed = fixPunctuationIssues(test);
    console.log(`Test ${index + 1}:`);
    console.log(`  Input:  "${test}"`);
    console.log(`  Output: "${fixed}"`);
    console.log(`  Fixed:  ${test !== fixed ? '✅' : '❌'}`);
    console.log('');
  });
  
  console.log('2. TESTING QUOTATION MARK SPACING');
  console.log('=' .repeat(40));
  testCases.quotationMarks.forEach((test, index) => {
    const fixed = fixQuotationMarkSpacing(test);
    console.log(`Test ${index + 1}:`);
    console.log(`  Input:  "${test}"`);
    console.log(`  Output: "${fixed}"`);
    console.log(`  Fixed:  ${test !== fixed ? '✅' : '❌'}`);
    console.log('');
  });
  
  console.log('3. TESTING CAPITALIZATION PRESERVATION');
  console.log('=' .repeat(40));
  testCases.capitalization.forEach((test, index) => {
    const { protectedResult, protectionMap } = protectCapitalizationAndTerms(test, ['AI', 'API', 'LLM']);
    let restored = protectedResult;
    protectionMap.forEach((original, placeholder) => {
      restored = restored.replace(new RegExp(placeholder, 'g'), original);
    });
    
    console.log(`Test ${index + 1}:`);
    console.log(`  Input:     "${test}"`);
    console.log(`  Protected: "${protectedResult}"`);
    console.log(`  Restored:  "${restored}"`);
    console.log(`  Preserved: ${test === restored ? '✅' : '❌'}`);
    console.log('');
  });
  
  console.log('4. TESTING TECHNICAL MODE ENHANCEMENTS');
  console.log('=' .repeat(40));
  testCases.technical.forEach((test, index) => {
    const enhanced = applyTechnicalOptimizations(test);
    console.log(`Test ${index + 1}:`);
    console.log(`  Input:    "${test}"`);
    console.log(`  Enhanced: "${enhanced}"`);
    console.log(`  Changed:  ${test !== enhanced ? '✅' : '❌'}`);
    console.log('');
  });
  
  console.log('5. COMPREHENSIVE TEST WITH REAL CONTENT');
  console.log('=' .repeat(40));
  
  const realContent = `AI systems work with "advanced"algorithms to make better results. The API will use LLM technology:. Users can get data and save files. Microsoft provides cloud services.`;
  
  console.log(`Original: "${realContent}"`);
  
  // Apply all fixes
  let processed = realContent;
  processed = fixPunctuationIssues(processed);
  processed = fixQuotationMarkSpacing(processed);
  
  const { protectedResult, protectionMap } = protectCapitalizationAndTerms(processed, ['AI', 'API', 'LLM', 'Microsoft']);
  processed = protectedResult;
  processed = applyTechnicalOptimizations(processed);
  
  // Restore protected terms
  protectionMap.forEach((original, placeholder) => {
    processed = processed.replace(new RegExp(placeholder, 'g'), original);
  });
  
  console.log(`Processed: "${processed}"`);
  
  // Check improvements
  const improvements = [];
  if (!processed.includes(':.')) improvements.push('Fixed double punctuation');
  if (!processed.includes('"advanced"algorithms')) improvements.push('Fixed quotation spacing');
  if (processed.includes('AI') && processed.includes('API') && processed.includes('LLM')) {
    improvements.push('Preserved capitalization');
  }
  if (processed !== realContent) improvements.push('Applied technical enhancements');
  
  console.log('\nImprovements applied:');
  improvements.forEach(imp => console.log(`  ✅ ${imp}`));
}

// Helper functions (simplified versions for testing)
function fixPunctuationIssues(text) {
  let result = text;
  result = result.replace(/([:.!?])\./g, '$1');
  result = result.replace(/\s+([.!?:;,])/g, '$1');
  result = result.replace(/\s{2,}/g, ' ');
  return result;
}

function fixQuotationMarkSpacing(text) {
  let result = text;
  result = result.replace(/(\w)"([^"])/g, '$1 "$2');
  result = result.replace(/([^"])"(\w)/g, '$1" $2');
  result = result.replace(/\s{2,}"/g, ' "');
  result = result.replace(/"\s{2,}/g, '" ');
  result = result.replace(/"([a-zA-Z])/g, '" $1');
  result = result.replace(/([a-zA-Z])"/g, '$1 "');
  result = result.replace(/(\w)"(\w)/g, '$1" $2');
  result = result.replace(/\s{2,}/g, ' ');
  return result;
}

function protectCapitalizationAndTerms(text, protectedTerms) {
  let result = text;
  const protectionMap = new Map();
  
  const capitalizedWords = text.match(/\b[A-Z][A-Za-z]*\b/g) || [];
  const uniqueCapitalizedWords = Array.from(new Set(capitalizedWords));
  
  const acronyms = text.match(/\b[A-Z]{2,}\b/g) || [];
  const uniqueAcronyms = Array.from(new Set(acronyms));
  
  const allTerms = protectedTerms.concat(uniqueCapitalizedWords).concat(uniqueAcronyms);
  const allProtectedTerms = Array.from(new Set(allTerms));
  
  allProtectedTerms.forEach((term, index) => {
    const placeholder = `__PROTECTED_TERM_${index}__`;
    const regex = new RegExp(`\\b${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
    result = result.replace(regex, placeholder);
    protectionMap.set(placeholder, term);
  });
  
  return { protectedResult: result, protectionMap };
}

function applyTechnicalOptimizations(text) {
  let result = text;
  
  const technicalReplacements = {
    'use': ['implement', 'utilize', 'deploy'],
    'make': ['generate', 'create', 'produce'],
    'get': ['retrieve', 'obtain', 'acquire'],
    'save': ['store', 'preserve', 'archive'],
    'work': ['function', 'operate', 'perform']
  };
  
  Object.entries(technicalReplacements).forEach(([casual, technicalOptions]) => {
    if (Math.random() < 0.6) {
      const regex = new RegExp(`\\b${casual}\\b`, 'gi');
      const selectedTechnical = technicalOptions[Math.floor(Math.random() * technicalOptions.length)];
      result = result.replace(regex, selectedTechnical);
    }
  });
  
  return result;
}

// Run the test
testAllImprovements();
