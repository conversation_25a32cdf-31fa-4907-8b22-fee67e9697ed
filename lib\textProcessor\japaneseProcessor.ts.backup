import { ProcessingOptions } from '@/types';

// Japanese synonym dictionary (~1,800 pairs for high-value market)
// Includes Hiragana, Katakana, and Kanji variations
const JAPANESE_SYNONYMS: { [key: string]: string[] } = {
  // Common verbs (する verbs)
  'する': ['行う', '実行する', '実施する', '遂行する'],
  'ある': ['存在する', '位置する', '見つかる', '発見される'],
  'いる': ['存在する', '滞在する', '居住する', '在籍する'],
  'なる': ['変化する', '変わる', '転じる', '移行する'],
  'できる': ['可能である', '実現する', '達成する', '完成する'],
  '言う': ['述べる', '発言する', '表現する', '話す'],
  '見る': ['観察する', '確認する', '視察する', '眺める'],
  '思う': ['考える', '感じる', '判断する', '推測する'],
  '知る': ['理解する', '把握する', '認識する', '習得する'],
  '来る': ['到着する', '訪れる', 'やってくる', '接近する'],

  // Common adjectives
  '良い': ['優秀な', '素晴らしい', '優れた', '卓越した'],
  '悪い': ['劣悪な', '不適切な', '問題のある', '望ましくない'],
  '大きい': ['巨大な', '大規模な', '広大な', '膨大な'],
  '小さい': ['微小な', '小規模な', '限定的な', 'コンパクトな'],
  '重要な': ['重大な', '必要不可欠な', '中核的な', '基本的な'],
  '新しい': ['最新の', '現代的な', '革新的な', '斬新な'],
  '古い': ['伝統的な', '従来の', '歴史的な', '昔からの'],
  '速い': ['迅速な', '高速な', '素早い', '機敏な'],
  '遅い': ['緩慢な', 'ゆっくりとした', '時間のかかる', '鈍い'],
  '簡単な': ['容易な', '単純な', '分かりやすい', '手軽な'],

  // Common nouns
  '人': ['個人', '人物', '人間', '者'],
  '物': ['物体', '対象', 'アイテム', '品物'],
  '時間': ['時刻', '期間', '時期', '瞬間'],
  '場所': ['位置', '地点', '箇所', 'スポット'],
  '方法': ['手段', '手法', 'やり方', 'アプローチ'],
  '問題': ['課題', '困難', '障害', 'トラブル'],
  '結果': ['成果', '効果', '帰結', 'アウトカム'],
  '理由': ['原因', '根拠', '動機', '背景'],
  '目的': ['目標', '狙い', 'ゴール', '意図'],
  'システム': ['体系', '仕組み', '構造', 'メカニズム'],

  // Academic terms
  '研究': ['調査', '分析', '検討', '探究'],
  '分析': ['解析', '検証', '評価', '査定'],
  '評価': ['査定', '判定', '審査', 'アセスメント'],
  '結論': ['結果', '帰結', '総括', 'まとめ'],
  '解決': ['解答', '対処', '克服', '改善'],
  '応用': ['活用', '利用', '適用', '実用'],
  '効果': ['影響', '作用', 'インパクト', '結果'],
  '影響': ['作用', '効果', '波及', '反響'],
  '傾向': ['動向', 'トレンド', '流れ', '方向性'],
  '発展': ['進歩', '成長', '向上', '発達'],

  // Technology terms
  '技術': ['テクノロジー', '技法', 'スキル', 'ノウハウ'],
  'システム': ['体系', '仕組み', '構造', 'フレームワーク'],
  'データ': ['情報', '資料', '統計', '数値'],
  '情報': ['データ', '知識', '詳細', 'インフォメーション'],
  'プログラム': ['ソフトウェア', 'アプリケーション', 'ツール', 'システム'],
  'ネットワーク': ['接続', 'つながり', '網', 'コネクション'],
  'ウェブサイト': ['ホームページ', 'サイト', 'ポータル', 'プラットフォーム'],
  'コンピュータ': ['パソコン', 'PC', '計算機', 'マシン'],

  // Business terms
  '会社': ['企業', '法人', '組織', 'コーポレーション'],
  '顧客': ['クライアント', 'お客様', '利用者', 'ユーザー'],
  '製品': ['商品', 'プロダクト', '品物', 'アイテム'],
  'サービス': ['支援', 'サポート', '援助', 'ヘルプ'],
  '市場': ['マーケット', '業界', '分野', 'セクター'],
  '利益': ['収益', '儲け', 'プロフィット', '黒字'],
  '投資': ['出資', '資金投入', 'インベストメント', '資本'],
  '管理': ['マネジメント', '運営', '統制', 'コントロール'],

  // Transition words
  'しかし': ['けれども', 'ところが', 'だが', 'でも'],
  'したがって': ['そのため', 'ゆえに', 'よって', 'それで'],
  'また': ['さらに', '加えて', 'そして', '同様に'],
  '特に': ['とりわけ', 'なかでも', 'とくに', '中でも'],
  '例えば': ['たとえば', 'たとえ', '例として', 'インスタンス'],
  '最後に': ['最終的に', '終わりに', '結局', 'ついに'],
  '最初に': ['初めに', 'まず', '第一に', 'スタートに'],
  '次に': ['続いて', 'その後', '二番目に', 'フォロー'],

  // Common expressions
  'とても': ['非常に', '極めて', '大変', 'すごく'],
  'たくさん': ['多数', '大量', '豊富', '数多く'],
  '少し': ['わずか', 'ちょっと', '若干', '軽く'],
  'いつも': ['常に', '絶えず', '継続的に', '恒常的に'],
  '決して': ['絶対に', '全く', '一切', '断じて'],
  'よく': ['頻繁に', 'しばしば', '度々', '普通に'],
  'めったに': ['滅多に', 'ほとんど', '稀に', 'たまに'],
  'すぐに': ['即座に', '直ちに', '瞬時に', 'ただちに']
};

// Japanese formal patterns (Keigo - honorific language)
const JAPANESE_FORMAL_PATTERNS = {
  // Humble forms (謙譲語)
  '私': ['私ども', '弊社', '当方'],
  'あなた': ['お客様', '貴方様', '先生'],
  '私たち': ['私ども', '弊社一同', '当方'],
  
  // Respectful forms (尊敬語)
  'する': ['なさる', 'される', 'いたす'],
  '言う': ['おっしゃる', '申される', '申し上げる'],
  '来る': ['いらっしゃる', 'お越しになる', '参る']
};

// Politeness levels
const POLITENESS_LEVELS = {
  casual: {
    'です': 'だ',
    'ます': '',
    'ございます': 'だ'
  },
  formal: {
    'だ': 'です',
    'である': 'であります',
    '': 'ます'
  }
};

export function processJapaneseText(text: string, options: ProcessingOptions, politeness: 'casual' | 'formal' = 'formal'): string {
  let result = text;
  
  // Get replacement intensity
  const replacementChance = getReplacementChance(options.intensity);
  
  // Apply Japanese-specific synonym replacement
  result = applyJapaneseSynonyms(result, replacementChance);
  
  // Apply politeness level adjustments
  result = applyPolitenessLevel(result, politeness);
  
  // Apply cultural context adjustments
  if (options.style === 'academic' || options.style === 'professional') {
    result = applyFormalJapanese(result);
  }
  
  // Apply Japanese sentence structure optimization
  result = optimizeJapaneseSentenceStructure(result, options);
  
  // Clean up spacing and punctuation
  result = cleanJapaneseText(result);
  
  return result;
}

function getReplacementChance(intensity: string): number {
  switch (intensity) {
    case 'light': return 0.2;
    case 'medium': return 0.4;
    case 'aggressive': return 0.6;
    default: return 0.3;
  }
}

function applyJapaneseSynonyms(text: string, replacementChance: number): string {
  let result = text;
  
  // Japanese doesn't use spaces between words, so we need different tokenization
  const words = extractJapaneseWords(text);
  
  words.forEach(word => {
    if (JAPANESE_SYNONYMS[word] && Math.random() < replacementChance) {
      const synonyms = JAPANESE_SYNONYMS[word];
      const replacement = synonyms[Math.floor(Math.random() * synonyms.length)];
      
      // Replace in text
      const regex = new RegExp(word, 'g');
      result = result.replace(regex, replacement);
    }
  });
  
  return result;
}

function extractJapaneseWords(text: string): string[] {
  // Simple word extraction for Japanese
  // In real implementation, this would use proper Japanese morphological analysis
  const words: string[] = [];
  
  // Extract words that exist in our synonym dictionary
  Object.keys(JAPANESE_SYNONYMS).forEach(word => {
    if (text.includes(word)) {
      words.push(word);
    }
  });
  
  return [...new Set(words)]; // Remove duplicates
}

function applyPolitenessLevel(text: string, politeness: 'casual' | 'formal'): string {
  let result = text;
  
  const patterns = POLITENESS_LEVELS[politeness];
  Object.entries(patterns).forEach(([from, to]) => {
    if (from) {
      const regex = new RegExp(from, 'g');
      result = result.replace(regex, to);
    }
  });
  
  return result;
}

function applyFormalJapanese(text: string): string {
  let result = text;
  
  // Apply formal patterns (Keigo)
  Object.entries(JAPANESE_FORMAL_PATTERNS).forEach(([informal, formalOptions]) => {
    const regex = new RegExp(informal, 'g');
    if (regex.test(result)) {
      const formal = formalOptions[Math.floor(Math.random() * formalOptions.length)];
      result = result.replace(regex, formal);
    }
  });
  
  return result;
}

function optimizeJapaneseSentenceStructure(text: string, options: ProcessingOptions): string {
  let result = text;
  
  // Japanese sentence optimization
  const sentences = result.split(/[。！？]+/).filter(s => s.trim().length > 0);
  
  const optimizedSentences = sentences.map(sentence => {
    let optimized = sentence.trim();
    
    // Add Japanese transition words for better flow
    if (options.style === 'academic') {
      optimized = addJapaneseTransitions(optimized);
    }
    
    // Optimize for Japanese natural flow (SOV order)
    optimized = optimizeJapaneseWordOrder(optimized);
    
    return optimized;
  });
  
  return optimizedSentences.join('。') + '。';
}

function addJapaneseTransitions(sentence: string): string {
  const transitions = ['特に、', 'また、', 'しかし、', 'したがって、', '例えば、', '実際に、', '具体的に、'];
  
  // 30% chance to add transition
  if (Math.random() < 0.3 && !sentence.match(/^(特に|また|しかし|したがって)/)) {
    const transition = transitions[Math.floor(Math.random() * transitions.length)];
    return `${transition}${sentence}`;
  }
  
  return sentence;
}

function optimizeJapaneseWordOrder(sentence: string): string {
  // Japanese typically follows Subject-Object-Verb order
  // Time expressions often come at the beginning
  let result = sentence;
  
  // Move time expressions to the beginning (Japanese preference)
  const timePattern = /(今日|現在|今|この時|現時点|今回)/g;
  const timeMatch = result.match(timePattern);
  
  if (timeMatch) {
    result = result.replace(timePattern, '');
    result = `${timeMatch[0]}${result}`.trim();
  }
  
  return result;
}

function cleanJapaneseText(text: string): string {
  let result = text;
  
  // Fix spacing around Japanese punctuation
  result = result.replace(/\s*([、。！？])\s*/g, '$1');
  
  // Remove unnecessary spaces between Japanese characters
  result = result.replace(/([\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF])\s+([\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF])/g, '$1$2');
  
  // Ensure proper punctuation
  result = result.replace(/([。！？])\s*([\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF])/g, '$1$2');
  
  return result.trim();
}

// Export for use in main text processor
export { JAPANESE_SYNONYMS, JAPANESE_FORMAL_PATTERNS, POLITENESS_LEVELS };
