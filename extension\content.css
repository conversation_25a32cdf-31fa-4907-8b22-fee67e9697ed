/* Content script styles for GhostLayer Chrome Extension */
/* These styles are injected into web pages to style the floating widget and modals */

/* Reset and base styles for extension elements */
#ghostlayer-widget,
#ghostlayer-widget *,
.gl-result-modal,
.gl-result-modal *,
.gl-notification,
.gl-notification * {
  all: initial;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  box-sizing: border-box !important;
}

/* Floating Widget Styles */
#ghostlayer-widget {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 2147483647 !important; /* Maximum z-index */
  transform: translateX(100%) !important;
  transition: transform 0.3s ease !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
}

#ghostlayer-widget.show {
  transform: translateX(0) !important;
}

.gl-widget-container {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
  width: 280px !important;
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
}

.gl-widget-header {
  display: flex !important;
  align-items: center !important;
  padding: 12px 16px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  background: rgba(255, 255, 255, 0.05) !important;
  border-radius: 12px 12px 0 0 !important;
}

.gl-logo {
  font-size: 18px !important;
  margin-right: 8px !important;
}

.gl-title {
  font-weight: 600 !important;
  flex: 1 !important;
  background: linear-gradient(45deg, #60a5fa, #a855f7) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.gl-close {
  background: none !important;
  border: none !important;
  color: #94a3b8 !important;
  font-size: 18px !important;
  cursor: pointer !important;
  padding: 0 !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
  transition: all 0.2s !important;
}

.gl-close:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

.gl-widget-content {
  padding: 16px !important;
}

.gl-quick-actions {
  display: flex !important;
  flex-direction: column !important;
  gap: 8px !important;
  margin-bottom: 16px !important;
}

.gl-btn {
  padding: 10px 12px !important;
  border: none !important;
  border-radius: 8px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 6px !important;
  text-decoration: none !important;
}

.gl-btn-primary {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6) !important;
  color: white !important;
}

.gl-btn-primary:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
}

.gl-btn-secondary {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #e2e8f0 !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.gl-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

.gl-style-selector {
  margin-bottom: 16px !important;
}

.gl-style-selector label {
  display: block !important;
  margin-bottom: 6px !important;
  font-size: 12px !important;
  color: #94a3b8 !important;
  font-weight: 500 !important;
}

.gl-style-selector select {
  width: 100% !important;
  padding: 8px 12px !important;
  background: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 6px !important;
  color: white !important;
  font-size: 13px !important;
}

.gl-stats {
  display: flex !important;
  justify-content: space-between !important;
  padding-top: 12px !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.gl-stat {
  text-align: center !important;
}

.gl-stat-label {
  display: block !important;
  font-size: 11px !important;
  color: #94a3b8 !important;
  margin-bottom: 2px !important;
}

.gl-stat-value {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #60a5fa !important;
}

/* Result Modal Styles */
.gl-result-modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.8) !important;
  z-index: 2147483647 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 20px !important;
}

.gl-result-content {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
  border-radius: 16px !important;
  max-width: 600px !important;
  width: 100% !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.gl-result-header {
  padding: 20px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.gl-result-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.gl-result-body {
  padding: 20px !important;
}

.gl-comparison {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 16px !important;
  margin-bottom: 20px !important;
}

@media (max-width: 768px) {
  .gl-comparison {
    grid-template-columns: 1fr !important;
  }
}

.gl-text-box {
  padding: 16px !important;
  border-radius: 8px !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.gl-text-box.original {
  background: rgba(239, 68, 68, 0.1) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

.gl-text-box.humanized {
  background: rgba(34, 197, 94, 0.1) !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
}

.gl-text-label {
  font-size: 12px !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.gl-text-label.original {
  color: #fca5a5 !important;
}

.gl-text-label.humanized {
  color: #86efac !important;
}

.gl-text-content {
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: #e2e8f0 !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

.gl-result-actions {
  display: flex !important;
  gap: 12px !important;
  justify-content: center !important;
  padding-top: 16px !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  flex-wrap: wrap !important;
}

/* Notification Styles */
.gl-notification {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 2147483647 !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  color: white !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  transform: translateX(100%) !important;
  transition: transform 0.3s ease !important;
  max-width: 300px !important;
  word-wrap: break-word !important;
}

.gl-notification.show {
  transform: translateX(0) !important;
}

.gl-notification.success {
  background: linear-gradient(45deg, #10b981, #059669) !important;
}

.gl-notification.error {
  background: linear-gradient(45deg, #ef4444, #dc2626) !important;
}

.gl-notification.warning {
  background: linear-gradient(45deg, #f59e0b, #d97706) !important;
}

.gl-notification.info {
  background: linear-gradient(45deg, #3b82f6, #2563eb) !important;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  #ghostlayer-widget {
    right: 10px !important;
    top: 10px !important;
  }
  
  .gl-widget-container {
    width: 260px !important;
  }
  
  .gl-result-modal {
    padding: 10px !important;
  }
  
  .gl-result-content {
    max-height: 90vh !important;
  }
  
  .gl-notification {
    right: 10px !important;
    top: 10px !important;
    max-width: 250px !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .gl-widget-container,
  .gl-result-content {
    border: 2px solid white !important;
  }
  
  .gl-btn {
    border: 1px solid white !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  #ghostlayer-widget,
  .gl-notification,
  .gl-btn {
    transition: none !important;
  }
}
