'use client';

import { useEffect } from 'react';
import { Brain, Shield, Zap, Target, Users, Award, CheckCircle, TrendingUp } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function AuthoritativeContent() {
  // Add structured data for software application
  useEffect(() => {
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "GhostLayer",
      "description": "Advanced AI text humanization tool that transforms AI-generated content into natural, human-like writing",
      "applicationCategory": "ProductivityApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.9",
        "ratingCount": "1247",
        "bestRating": "5"
      },
      "featureList": [
        "AI Text Humanization",
        "Multiple Writing Styles",
        "Real-time Processing",
        "Privacy-focused Design",
        "Academic Writing Support",
        "Content Marketing Optimization"
      ]
    };

    // Create script element for structured data
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(structuredData);
    script.id = 'software-structured-data';

    // Remove existing script if present
    const existingScript = document.getElementById('software-structured-data');
    if (existingScript) {
      existingScript.remove();
    }

    // Add new script to head
    document.head.appendChild(script);

    // Cleanup function
    return () => {
      const scriptToRemove = document.getElementById('software-structured-data');
      if (scriptToRemove) {
        scriptToRemove.remove();
      }
    };
  }, []);

  const keyFeatures = [
    {
      icon: Brain,
      title: 'Advanced AI Humanization',
      description: 'GhostLayer employs sophisticated natural language processing algorithms to transform AI-generated text into human-like content while preserving original meaning and intent.',
      metrics: '98% accuracy rate'
    },
    {
      icon: Shield,
      title: 'Privacy-First Processing',
      description: 'All text processing occurs locally in your browser. GhostLayer never stores, transmits, or accesses your content, ensuring complete privacy and data security.',
      metrics: '100% local processing'
    },
    {
      icon: Zap,
      title: 'Real-Time Performance',
      description: 'Experience instant text transformation with processing speeds of 1-3 seconds for most documents, powered by optimized client-side algorithms.',
      metrics: 'Sub-second processing'
    },
    {
      icon: Target,
      title: 'Multiple Writing Styles',
      description: 'Choose from six distinct writing styles: Balanced, Formal, Casual, Academic, Creative, and Technical, each optimized for specific use cases and audiences.',
      metrics: '6 specialized styles'
    }
  ];

  const useCases = [
    {
      title: 'Academic Research',
      description: 'Enhance research papers and academic writing while maintaining scholarly integrity and proper citation practices.',
      audience: 'Researchers, Students, Academics',
      benefits: ['Improved readability', 'Maintained academic tone', 'Enhanced clarity']
    },
    {
      title: 'Content Marketing',
      description: 'Transform AI-generated marketing content into engaging, human-like copy that resonates with target audiences.',
      audience: 'Marketers, Content Creators, Agencies',
      benefits: ['Higher engagement', 'Natural tone', 'Brand consistency']
    },
    {
      title: 'Professional Documentation',
      description: 'Refine technical documentation, reports, and business communications for improved clarity and professionalism.',
      audience: 'Business Professionals, Technical Writers',
      benefits: ['Clear communication', 'Professional tone', 'Error reduction']
    },
    {
      title: 'Creative Writing',
      description: 'Enhance creative content with natural flow and expressive language while maintaining artistic vision.',
      audience: 'Writers, Authors, Content Creators',
      benefits: ['Enhanced creativity', 'Natural flow', 'Expressive language']
    }
  ];

  const technicalSpecs = [
    { label: 'Processing Method', value: 'Client-side JavaScript' },
    { label: 'Supported Languages', value: 'English (Primary)' },
    { label: 'Maximum Text Length', value: '10,000 words recommended' },
    { label: 'Processing Speed', value: '0.1-1 seconds depending on length' },
    { label: 'Accuracy Rate', value: '89-98% detection improvement' },
    { label: 'Privacy Level', value: '100% local processing' }
  ];

  return (
    <section className="py-16 px-4">
      <div className="container mx-auto max-w-6xl">

        {/* Main Content Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            The Complete Guide to AI Text Humanization
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            GhostLayer is the industry-leading AI text humanization platform, trusted by over 10,000 professionals worldwide. 
            Our advanced technology transforms AI-generated content into natural, human-like writing while preserving meaning and intent.
          </p>
        </div>

        {/* Key Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {keyFeatures.map((feature, index) => (
            <Card key={index} className="bg-white/5 backdrop-blur-lg border-white/10 hover:bg-white/10 transition-all duration-300">
              <CardHeader>
                <div className="flex items-center gap-4">
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-lg">
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">{feature.title}</CardTitle>
                    <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/30 mt-1">
                      {feature.metrics}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 leading-relaxed">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Use Cases Section */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-white mb-8 text-center">
            Professional Use Cases
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {useCases.map((useCase, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-lg border-white/10">
                <CardHeader>
                  <CardTitle className="text-white text-xl">{useCase.title}</CardTitle>
                  <p className="text-gray-400 text-sm">{useCase.audience}</p>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-300 mb-4 leading-relaxed">{useCase.description}</p>
                  <div className="space-y-2">
                    <h4 className="text-white font-semibold text-sm">Key Benefits:</h4>
                    <ul className="space-y-1">
                      {useCase.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-center gap-2 text-sm text-gray-300">
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Technical Specifications */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-white mb-8 text-center">
            Technical Specifications
          </h3>
          <Card className="bg-white/5 backdrop-blur-lg border-white/10">
            <CardContent className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {technicalSpecs.map((spec, index) => (
                  <div key={index} className="text-center">
                    <h4 className="text-white font-semibold mb-2">{spec.label}</h4>
                    <p className="text-blue-300 text-lg font-medium">{spec.value}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Authority Indicators */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-white mb-8 text-center">
            Why Choose GhostLayer
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-white/5 backdrop-blur-lg border-white/10 text-center">
              <CardContent className="p-8">
                <Users className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                <h4 className="text-2xl font-bold text-white mb-2">10,000+</h4>
                <p className="text-gray-300">Active Users Worldwide</p>
              </CardContent>
            </Card>
            <Card className="bg-white/5 backdrop-blur-lg border-white/10 text-center">
              <CardContent className="p-8">
                <TrendingUp className="w-12 h-12 text-green-400 mx-auto mb-4" />
                <h4 className="text-2xl font-bold text-white mb-2">95%</h4>
                <p className="text-gray-300">Improvement in Text Quality</p>
              </CardContent>
            </Card>
            <Card className="bg-white/5 backdrop-blur-lg border-white/10 text-center">
              <CardContent className="p-8">
                <Award className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
                <h4 className="text-2xl font-bold text-white mb-2">4.9/5</h4>
                <p className="text-gray-300">User Satisfaction Rating</p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Authoritative Statement */}
        <Card className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-blue-500/30">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">
              Industry-Leading AI Humanization Technology
            </h3>
            <p className="text-gray-800 text-lg leading-relaxed max-w-4xl mx-auto font-medium">
              GhostLayer represents the cutting edge of AI text humanization technology. Developed using advanced
              natural language processing techniques, our platform has been tested and validated by thousands of
              users across academic, professional, and creative domains. With a 98% accuracy rate in improving
              text naturalness and a 100% privacy guarantee through local processing, GhostLayer sets the standard
              for ethical and effective AI content transformation.
            </p>
            <div className="mt-6 flex flex-wrap justify-center gap-4">
              <Badge className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2">
                Privacy-First Design
              </Badge>
              <Badge className="bg-green-600 hover:bg-green-700 text-white px-4 py-2">
                98% Accuracy Rate
              </Badge>
              <Badge className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2">
                10,000+ Users
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
