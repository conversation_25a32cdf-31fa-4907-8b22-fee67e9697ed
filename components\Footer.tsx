'use client';

import { useEffect } from 'react';
import { Brain, Github, Twitter, Linkedin, Mail, Shield, Zap, Users } from 'lucide-react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  // Add structured data for organization
  useEffect(() => {
    const organizationSchema = {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "GhostLayer",
      "url": "https://ghostlayer.io.vn",
      "logo": "https://ghostlayer.io.vn/logo.jpg",
      "description": "Advanced AI text humanization platform for transforming AI-generated content into natural, human-like writing",
      "foundingDate": "2024",
      "sameAs": [
        "https://x.com/HectorTa_1711",
        "https://linkedin.com/in/hector-ta-743b0172/",
        "https://github.com/ghostlayer"
      ],
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "email": "<EMAIL>"
      }
    };

    // Create script element for structured data
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(organizationSchema);
    script.id = 'organization-structured-data';

    // Remove existing script if present
    const existingScript = document.getElementById('organization-structured-data');
    if (existingScript) {
      existingScript.remove();
    }

    // Add new script to head
    document.head.appendChild(script);

    // Cleanup function
    return () => {
      const scriptToRemove = document.getElementById('organization-structured-data');
      if (scriptToRemove) {
        scriptToRemove.remove();
      }
    };
  }, []);

  const quickLinks = [
    { name: 'How It Works', href: '#features' },
    { name: 'FAQ', href: '#faq' },
    { name: 'Testimonials', href: '#testimonials' },
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' }
  ];

  const features = [
    { name: 'AI Text Humanization', description: 'Transform AI content naturally' },
    { name: 'Multiple Writing Styles', description: '6 specialized writing modes' },
    { name: 'Privacy-First Design', description: '100% local processing' },
    { name: 'Real-Time Processing', description: 'Instant text transformation' }
  ];

  const stats = [
    { label: 'Users Worldwide', value: '10,000+' },
    { label: 'Text Processed', value: '1M+ words' },
    { label: 'Accuracy Rate', value: '95%' },
    { label: 'Privacy Level', value: '100%' }
  ];

  return (
    <footer className="bg-slate-900/50 backdrop-blur-lg border-t border-white/10 mt-16">
      <div className="container mx-auto px-4 py-16">

        {/* Main Footer Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-12 mb-12">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="flex items-center gap-4 mb-6">
              <Image
                src="/logo.jpg"
                alt="GhostLayer Logo"
                width={48}
                height={48}
                className="w-12 h-12 rounded-xl object-cover"
              />
              <div>
                <h3 className="text-2xl font-bold text-white">GhostLayer</h3>
                <p className="text-sm text-gray-400">AI Text Humanization</p>
              </div>
            </div>
            <p className="text-gray-300 leading-relaxed mb-6">
              The industry-leading platform for transforming AI-generated content into natural, 
              human-like writing while preserving meaning and intent.
            </p>
            <div className="flex gap-4">
              <a href="https://x.com/HectorTa_1711" className="text-gray-400 hover:text-blue-400 transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="https://linkedin.com/in/hector-ta-743b0172/" className="text-gray-400 hover:text-blue-400 transition-colors">
                <Linkedin className="w-5 h-5" />
              </a>
              <a href="https://github.com/ghostlayer" className="text-gray-400 hover:text-blue-400 transition-colors">
                <Github className="w-5 h-5" />
              </a>
              <a href="mailto:<EMAIL>" className="text-gray-400 hover:text-blue-400 transition-colors">
                <Mail className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-6">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors"
                    onClick={link.href.startsWith('#') ? (e) => {
                      e.preventDefault();
                      const element = document.querySelector(link.href);
                      if (element) {
                        element.scrollIntoView({
                          behavior: 'smooth',
                          block: 'start'
                        });
                      }
                    } : undefined}
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Features */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-6">Key Features</h4>
            <ul className="space-y-4">
              {features.map((feature, index) => (
                <li key={index}>
                  <h5 className="text-white font-medium">{feature.name}</h5>
                  <p className="text-sm text-gray-400">{feature.description}</p>
                </li>
              ))}
            </ul>
          </div>

          {/* Stats */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-6">Platform Stats</h4>
            <div className="grid grid-cols-2 gap-4">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl font-bold text-blue-400 mb-1">{stat.value}</div>
                  <div className="text-xs text-gray-400">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <Card className="bg-white/5 backdrop-blur-lg border-white/10 mb-12">
          <CardContent className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="flex flex-col items-center">
                <Shield className="w-12 h-12 text-green-400 mb-4" />
                <h5 className="text-white font-semibold mb-2">100% Privacy Guaranteed</h5>
                <p className="text-sm text-gray-400">All processing happens locally in your browser. Your content never leaves your device.</p>
              </div>
              <div className="flex flex-col items-center">
                <Zap className="w-12 h-12 text-yellow-400 mb-4" />
                <h5 className="text-white font-semibold mb-2">Lightning Fast Processing</h5>
                <p className="text-sm text-gray-400">Advanced algorithms deliver results in seconds, not minutes.</p>
              </div>
              <div className="flex flex-col items-center">
                <Users className="w-12 h-12 text-blue-400 mb-4" />
                <h5 className="text-white font-semibold mb-2">Trusted by Professionals</h5>
                <p className="text-sm text-gray-400">Used by researchers, writers, and content creators worldwide.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* SEO-Rich Content */}
        <div className="mb-12">
          <Card className="bg-white/5 backdrop-blur-lg border-white/10">
            <CardContent className="p-8">
              <h4 className="text-xl font-bold text-white mb-4">About AI Text Humanization</h4>
              <p className="text-gray-300 leading-relaxed mb-4">
                AI text humanization is the process of transforming artificially generated content to make it sound more natural and human-like. 
                GhostLayer uses advanced natural language processing techniques to analyze and modify text patterns, sentence structures, 
                and vocabulary choices while preserving the original meaning and intent.
              </p>
              <p className="text-gray-300 leading-relaxed mb-6">
                Our platform supports multiple writing styles including academic, professional, creative, and casual tones, making it suitable 
                for researchers, content creators, students, and business professionals who need to enhance AI-generated content quality.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                  AI Text Humanization
                </Badge>
                <Badge variant="secondary" className="bg-purple-500/20 text-purple-300 border-purple-500/30">
                  Content Transformation
                </Badge>
                <Badge variant="secondary" className="bg-green-500/20 text-green-300 border-green-500/30">
                  Natural Language Processing
                </Badge>
                <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30">
                  Writing Enhancement
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/10 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-gray-400 text-sm">
              © {currentYear} GhostLayer. All rights reserved. Advanced AI text humanization technology.
            </div>
            <div className="flex items-center gap-6 text-sm text-gray-400">
              <a href="/privacy" className="hover:text-white transition-colors">Privacy Policy</a>
              <a href="/terms" className="hover:text-white transition-colors">Terms of Service</a>
              <a href="/contact" className="hover:text-white transition-colors">Contact</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
