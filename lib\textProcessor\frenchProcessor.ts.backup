import { ProcessingOptions } from '@/types';

// French synonym dictionary (~2,200 pairs for European market)
const FRENCH_SYNONYMS: { [key: string]: string[] } = {
  // Common verbs
  'être': ['constituer', 'représenter', 'demeurer', 'se trouver'],
  'avoir': ['posséder', 'détenir', 'disposer de', 'jouir de'],
  'faire': ['réaliser', 'effectuer', 'exécuter', 'accomplir'],
  'dire': ['exprimer', 'déclarer', 'affirmer', 'énoncer'],
  'voir': ['observer', 'contempler', 'apercevoir', 'distinguer'],
  'donner': ['accorder', 'octroyer', 'fournir', 'procurer'],
  'savoir': ['connaître', 'maîtriser', 'comprendre', 'saisir'],
  'vouloir': ['désirer', 'souhaiter', 'aspirer à', 'ambitionner'],
  'aller': ['se rendre', 'se diriger', 'partir', 'voyager'],
  'venir': ['arriver', 'parvenir', 'survenir', 'approcher'],

  // Common adjectives
  'bon': ['excellent', 'remarquable', 'formidable', 'exceptionnel'],
  'mauvais': ['médiocre', 'défaillant', 'inadéquat', 'déplorable'],
  'grand': ['immense', 'gigantesque', 'colossal', 'considérable'],
  'petit': ['minuscule', 'réduit', 'modeste', 'limité'],
  'important': ['essentiel', 'fondamental', 'crucial', 'primordial'],
  'nouveau': ['récent', 'moderne', 'contemporain', 'inédit'],
  'ancien': ['antique', 'traditionnel', 'historique', 'vétuste'],
  'rapide': ['véloce', 'prompt', 'expéditif', 'accéléré'],
  'lent': ['tardif', 'paresseux', 'nonchalant', 'traînard'],
  'facile': ['simple', 'aisé', 'élémentaire', 'accessible'],

  // Common nouns
  'personne': ['individu', 'être humain', 'personnage', 'citoyen'],
  'chose': ['objet', 'élément', 'article', 'item'],
  'temps': ['période', 'époque', 'moment', 'instant'],
  'lieu': ['endroit', 'emplacement', 'localisation', 'site'],
  'manière': ['façon', 'méthode', 'procédé', 'technique'],
  'problème': ['difficulté', 'obstacle', 'embarras', 'contrariété'],
  'résultat': ['conséquence', 'effet', 'aboutissement', 'produit'],
  'raison': ['motif', 'cause', 'fondement', 'justification'],
  'objectif': ['but', 'finalité', 'intention', 'dessein'],
  'système': ['structure', 'organisation', 'mécanisme', 'dispositif'],

  // Academic terms
  'recherche': ['étude', 'investigation', 'exploration', 'enquête'],
  'analyse': ['examen', 'évaluation', 'inspection', 'décortication'],
  'évaluation': ['appréciation', 'estimation', 'jugement', 'cotation'],
  'conclusion': ['déduction', 'inférence', 'aboutissement', 'résolution'],
  'solution': ['réponse', 'résolution', 'remède', 'alternative'],
  'application': ['usage', 'emploi', 'utilisation', 'mise en œuvre'],
  'effet': ['impact', 'conséquence', 'résultat', 'répercussion'],
  'influence': ['ascendant', 'emprise', 'action', 'incidence'],
  'tendance': ['inclination', 'propension', 'orientation', 'penchant'],
  'développement': ['évolution', 'progrès', 'croissance', 'expansion'],

  // Technology terms
  'technologie': ['technique', 'science appliquée', 'innovation', 'procédé'],
  'infrastructure': ['structure', 'organisation', 'ensemble', 'réseau'],
  'données': ['informations', 'statistiques', 'chiffres', 'éléments'],
  'information': ['renseignement', 'donnée', 'détail', 'contenu'],
  'programme': ['logiciel', 'application', 'outil', 'système'],
  'réseau': ['connexion', 'liaison', 'maillage', 'infrastructure'],
  'site web': ['site internet', 'portail', 'plateforme numérique', 'page web'],
  'ordinateur': ['machine', 'équipement', 'dispositif', 'terminal'],

  // Business terms
  'entreprise': ['société', 'compagnie', 'organisation', 'firme'],
  'client': ['consommateur', 'utilisateur', 'acheteur', 'clientèle'],
  'produit': ['article', 'marchandise', 'bien', 'manufacture'],
  'service': ['prestation', 'assistance', 'support', 'aide'],
  'marché': ['secteur', 'domaine commercial', 'place', 'débouché'],
  'profit': ['bénéfice', 'gain', 'rendement', 'rapport'],
  'investissement': ['placement', 'mise de fonds', 'capital', 'financement'],
  'gestion': ['administration', 'direction', 'management', 'pilotage'],

  // Transition words
  'cependant': ['néanmoins', 'toutefois', 'pourtant', 'malgré tout'],
  'par conséquent': ['donc', 'ainsi', 'en conséquence', 'de ce fait'],
  'en outre': ['de plus', 'également', 'par ailleurs', 'qui plus est'],
  'notamment': ['particulièrement', 'surtout', 'spécialement', 'principalement'],
  'par exemple': ['comme', 'tel que', 'à l\'instar de', 'à titre d\'exemple'],
  'enfin': ['finalement', 'pour finir', 'en dernier lieu', 'pour conclure'],
  'd\'abord': ['premièrement', 'en premier lieu', 'tout d\'abord', 'initialement'],
  'ensuite': ['puis', 'après', 'par la suite', 'secondement'],

  // Common expressions
  'très': ['extrêmement', 'particulièrement', 'remarquablement', 'considérablement'],
  'beaucoup': ['énormément', 'abondamment', 'largement', 'grandement'],
  'peu': ['faiblement', 'légèrement', 'modérément', 'parcimonieusement'],
  'toujours': ['constamment', 'continuellement', 'perpétuellement', 'invariablement'],
  'jamais': ['en aucun cas', 'nullement', 'point', 'aucunement'],
  'souvent': ['fréquemment', 'régulièrement', 'habituellement', 'couramment'],
  'rarement': ['peu souvent', 'exceptionnellement', 'sporadiquement', 'occasionnellement'],
  'immédiatement': ['instantanément', 'aussitôt', 'sur-le-champ', 'sans délai']
};

// French formal patterns
const FRENCH_FORMAL_PATTERNS = {
  // Formal expressions
  'je': ['l\'auteur', 'le soussigné', 'celui qui écrit'],
  'tu': ['vous', 'votre personne', 'le lecteur'],
  'nous': ['les auteurs', 'les soussignés', 'l\'équipe'],
  
  // Politeness markers
  's\'il vous plaît': ['je vous prie', 'veuillez', 'ayez l\'amabilité'],
  'merci': ['je vous remercie', 'mes remerciements', 'ma gratitude'],
  'pardon': ['excusez-moi', 'je vous prie de m\'excuser', 'veuillez m\'excuser']
};

export function processFrenchText(text: string, options: ProcessingOptions): string {
  let result = text;
  
  // Get replacement intensity
  const replacementChance = getReplacementChance(options.intensity);
  
  // Apply French-specific synonym replacement
  result = applyFrenchSynonyms(result, replacementChance);
  
  // Apply cultural context adjustments
  if (options.style === 'academic' || options.style === 'formal') {
    result = applyFormalFrench(result);
  }
  
  // Apply French sentence structure optimization
  result = optimizeFrenchSentenceStructure(result, options);
  
  // Clean up spacing and punctuation
  result = cleanFrenchText(result);
  
  return result;
}

function getReplacementChance(intensity: string): number {
  switch (intensity) {
    case 'light': return 0.2;
    case 'medium': return 0.4;
    case 'aggressive': return 0.6;
    default: return 0.3;
  }
}

function applyFrenchSynonyms(text: string, replacementChance: number): string {
  let result = text;
  
  // Split into words while preserving punctuation
  const words = text.split(/(\s+|[.,!?;:«»])/);
  
  for (let i = 0; i < words.length; i++) {
    const word = words[i].toLowerCase().trim();
    
    if (FRENCH_SYNONYMS[word] && Math.random() < replacementChance) {
      const synonyms = FRENCH_SYNONYMS[word];
      const replacement = synonyms[Math.floor(Math.random() * synonyms.length)];
      
      // Preserve original capitalization
      if (words[i][0] === words[i][0].toUpperCase()) {
        words[i] = replacement.charAt(0).toUpperCase() + replacement.slice(1);
      } else {
        words[i] = replacement;
      }
    }
  }
  
  return words.join('');
}

function applyFormalFrench(text: string): string {
  let result = text;
  
  // Apply formal patterns
  Object.entries(FRENCH_FORMAL_PATTERNS).forEach(([informal, formalOptions]) => {
    const regex = new RegExp(`\\b${informal}\\b`, 'gi');
    if (regex.test(result)) {
      const formal = formalOptions[Math.floor(Math.random() * formalOptions.length)];
      result = result.replace(regex, formal);
    }
  });
  
  return result;
}

function optimizeFrenchSentenceStructure(text: string, options: ProcessingOptions): string {
  let result = text;
  
  // French sentence optimization
  const sentences = result.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  const optimizedSentences = sentences.map(sentence => {
    let optimized = sentence.trim();
    
    // Add French transition words for better flow
    if (options.style === 'academic') {
      optimized = addFrenchTransitions(optimized);
    }
    
    // Optimize for French natural flow
    optimized = optimizeFrenchWordOrder(optimized);
    
    return optimized;
  });
  
  return optimizedSentences.join('. ') + '.';
}

function addFrenchTransitions(sentence: string): string {
  const transitions = [
    'Notamment,', 'En outre,', 'Cependant,', 'Par conséquent,', 
    'Par exemple,', 'En effet,', 'Concrètement,'
  ];
  
  // 30% chance to add transition
  if (Math.random() < 0.3 && !sentence.match(/^(Notamment|En outre|Cependant|Par conséquent)/)) {
    const transition = transitions[Math.floor(Math.random() * transitions.length)];
    return `${transition} ${sentence.toLowerCase()}`;
  }
  
  return sentence;
}

function optimizeFrenchWordOrder(sentence: string): string {
  // French follows Subject-Verb-Object order with some flexibility
  let result = sentence;
  
  // Move time expressions to the beginning (French preference)
  const timePattern = /\b(aujourd\'hui|maintenant|actuellement|à présent|en ce moment|présentement)\b/gi;
  const timeMatch = result.match(timePattern);
  
  if (timeMatch) {
    result = result.replace(timePattern, '');
    result = `${timeMatch[0]} ${result}`.trim();
  }
  
  return result;
}

function cleanFrenchText(text: string): string {
  let result = text;
  
  // Fix spacing around French punctuation
  result = result.replace(/\s+([.,!?;:])/g, '$1');
  result = result.replace(/([.,!?;:])\s*/g, '$1 ');
  
  // Handle French quotation marks
  result = result.replace(/\s*«\s*/g, '« ');
  result = result.replace(/\s*»\s*/g, ' »');
  
  // Remove multiple spaces
  result = result.replace(/\s{2,}/g, ' ');
  
  // Ensure proper capitalization after French sentence endings
  result = result.replace(/([.!?])\s*([a-zàâäéèêëïîôöùûüÿç])/g, 
    (match, punct, letter) => `${punct} ${letter.toUpperCase()}`);
  
  return result.trim();
}

// Export for use in main text processor
export { FRENCH_SYNONYMS, FRENCH_FORMAL_PATTERNS };
