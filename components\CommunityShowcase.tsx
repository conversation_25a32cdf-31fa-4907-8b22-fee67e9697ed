'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Trophy, 
  Star, 
  TrendingUp, 
  Users, 
  Heart, 
  MessageCircle,
  Share2,
  Award,
  Crown,
  Zap,
  Target,
  Flame
} from 'lucide-react';
import { analytics } from '@/lib/analytics';

interface CommunityPost {
  id: string;
  username: string;
  avatar: string;
  originalText: string;
  humanizedText: string;
  improvementScore: number;
  likes: number;
  comments: number;
  shares: number;
  timestamp: string;
  badges: string[];
  isLiked?: boolean;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: any;
  progress: number;
  maxProgress: number;
  unlocked: boolean;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

export default function CommunityShowcase() {
  const [activeTab, setActiveTab] = useState('trending');
  const [userStats, setUserStats] = useState({
    rank: 1247,
    totalScore: 8420,
    transformations: 23,
    avgImprovement: 87,
    streak: 5
  });

  // Mock community posts (in real app, this would come from API)
  const [communityPosts] = useState<CommunityPost[]>([
    {
      id: '1',
      username: 'ContentCreator_Pro',
      avatar: '👨‍💼',
      originalText: 'The implementation of artificial intelligence in modern business processes has significantly enhanced operational efficiency and productivity metrics across various industry sectors.',
      humanizedText: 'AI has revolutionized how businesses operate today. Companies across different industries are seeing real improvements in efficiency and productivity thanks to smart automation.',
      improvementScore: 94,
      likes: 156,
      comments: 23,
      shares: 45,
      timestamp: '2 hours ago',
      badges: ['🏆 Top Performer', '🔥 Viral Post'],
      isLiked: false
    },
    {
      id: '2',
      username: 'AcademicWriter',
      avatar: '👩‍🎓',
      originalText: 'Furthermore, the comprehensive analysis demonstrates that the utilization of advanced methodologies significantly contributes to enhanced outcomes.',
      humanizedText: 'Our detailed study shows that using better methods really helps improve results.',
      improvementScore: 91,
      likes: 89,
      comments: 12,
      shares: 28,
      timestamp: '4 hours ago',
      badges: ['📚 Academic Expert'],
      isLiked: true
    },
    {
      id: '3',
      username: 'TechBlogger',
      avatar: '👨‍💻',
      originalText: 'The optimization of user experience through innovative interface design paradigms represents a fundamental shift in contemporary digital product development.',
      humanizedText: 'Better UX design is changing how we build digital products today. New interface ideas are making apps more user-friendly than ever.',
      improvementScore: 88,
      likes: 67,
      comments: 8,
      shares: 19,
      timestamp: '6 hours ago',
      badges: ['💻 Tech Guru'],
      isLiked: false
    }
  ]);

  // Mock achievements
  const [achievements] = useState<Achievement[]>([
    {
      id: 'first_transformation',
      title: 'First Steps',
      description: 'Complete your first text transformation',
      icon: Zap,
      progress: 1,
      maxProgress: 1,
      unlocked: true,
      rarity: 'common'
    },
    {
      id: 'high_score',
      title: 'Perfectionist',
      description: 'Achieve 95%+ improvement score',
      icon: Target,
      progress: 2,
      maxProgress: 5,
      unlocked: false,
      rarity: 'rare'
    },
    {
      id: 'streak_master',
      title: 'Streak Master',
      description: 'Use GhostLayer for 7 days in a row',
      icon: Flame,
      progress: 5,
      maxProgress: 7,
      unlocked: false,
      rarity: 'epic'
    },
    {
      id: 'community_legend',
      title: 'Community Legend',
      description: 'Get 1000+ likes on your posts',
      icon: Crown,
      progress: 156,
      maxProgress: 1000,
      unlocked: false,
      rarity: 'legendary'
    }
  ]);

  const handleLike = (postId: string) => {
    analytics.trackShare('community_like');
    // In real app, this would update the backend
    console.log('Liked post:', postId);
  };

  const handleShare = (postId: string) => {
    analytics.trackShare('community_share');
    // In real app, this would open share dialog
    console.log('Shared post:', postId);
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'text-gray-400 border-gray-500';
      case 'rare': return 'text-blue-400 border-blue-500';
      case 'epic': return 'text-purple-400 border-purple-500';
      case 'legendary': return 'text-yellow-400 border-yellow-500';
      default: return 'text-gray-400 border-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      {/* User Stats Dashboard */}
      <Card className="bg-white/5 backdrop-blur-lg border-white/10">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Trophy className="w-5 h-5 text-yellow-400" />
            Your Community Stats
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">#{userStats.rank}</div>
              <div className="text-xs text-gray-400">Global Rank</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{userStats.totalScore.toLocaleString()}</div>
              <div className="text-xs text-gray-400">Total Score</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{userStats.transformations}</div>
              <div className="text-xs text-gray-400">Transformations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">{userStats.avgImprovement}%</div>
              <div className="text-xs text-gray-400">Avg Improvement</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-400">{userStats.streak}</div>
              <div className="text-xs text-gray-400">Day Streak</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-slate-800/50">
          <TabsTrigger value="trending" className="text-white">🔥 Trending</TabsTrigger>
          <TabsTrigger value="achievements" className="text-white">🏆 Achievements</TabsTrigger>
          <TabsTrigger value="leaderboard" className="text-white">👑 Leaderboard</TabsTrigger>
        </TabsList>

        <TabsContent value="trending" className="mt-6">
          <div className="space-y-4">
            {communityPosts.map((post) => (
              <Card key={post.id} className="bg-white/5 backdrop-blur-lg border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="text-2xl">{post.avatar}</div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-semibold text-white">{post.username}</span>
                        <span className="text-gray-400 text-sm">{post.timestamp}</span>
                        {post.badges.map((badge, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {badge}
                          </Badge>
                        ))}
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div className="bg-red-500/10 rounded-lg p-3 border border-red-500/20">
                          <h5 className="text-xs font-semibold text-red-400 mb-2">Before</h5>
                          <p className="text-gray-300 text-sm">{post.originalText}</p>
                        </div>
                        <div className="bg-green-500/10 rounded-lg p-3 border border-green-500/20">
                          <h5 className="text-xs font-semibold text-green-400 mb-2">After</h5>
                          <p className="text-gray-300 text-sm">{post.humanizedText}</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <Badge className="bg-green-500/20 text-green-400">
                          {post.improvementScore}% Improvement
                        </Badge>
                        
                        <div className="flex items-center gap-4">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleLike(post.id)}
                            className={`text-gray-400 hover:text-red-400 ${post.isLiked ? 'text-red-400' : ''}`}
                          >
                            <Heart className="w-4 h-4 mr-1" />
                            {post.likes}
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="text-gray-400 hover:text-blue-400"
                          >
                            <MessageCircle className="w-4 h-4 mr-1" />
                            {post.comments}
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleShare(post.id)}
                            className="text-gray-400 hover:text-green-400"
                          >
                            <Share2 className="w-4 h-4 mr-1" />
                            {post.shares}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="achievements" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {achievements.map((achievement) => {
              const IconComponent = achievement.icon;
              const progressPercentage = (achievement.progress / achievement.maxProgress) * 100;
              
              return (
                <Card key={achievement.id} className={`bg-white/5 backdrop-blur-lg border-white/10 ${achievement.unlocked ? 'ring-2 ring-green-500/50' : ''}`}>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className={`p-3 rounded-lg border-2 ${getRarityColor(achievement.rarity)} ${achievement.unlocked ? 'bg-green-500/20' : 'bg-gray-500/20'}`}>
                        <IconComponent className="w-6 h-6" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-white mb-1">{achievement.title}</h4>
                        <p className="text-gray-400 text-sm mb-3">{achievement.description}</p>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-400">Progress</span>
                            <span className="text-white">{achievement.progress}/{achievement.maxProgress}</span>
                          </div>
                          <div className="w-full bg-gray-700 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full transition-all duration-300 ${achievement.unlocked ? 'bg-green-500' : 'bg-blue-500'}`}
                              style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                            />
                          </div>
                        </div>
                        
                        {achievement.unlocked && (
                          <Badge className="mt-2 bg-green-500/20 text-green-400">
                            <Award className="w-3 h-3 mr-1" />
                            Unlocked!
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="leaderboard" className="mt-6">
          <Card className="bg-white/5 backdrop-blur-lg border-white/10">
            <CardHeader>
              <CardTitle className="text-white">Top Humanizers This Week</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { rank: 1, username: 'HumanizeKing', score: 15420, avatar: '👑' },
                  { rank: 2, username: 'TextMaster_Pro', score: 14890, avatar: '🥈' },
                  { rank: 3, username: 'ContentWizard', score: 13750, avatar: '🥉' },
                  { rank: 4, username: 'WriteRight', score: 12340, avatar: '⭐' },
                  { rank: 5, username: 'You', score: userStats.totalScore, avatar: '👤' }
                ].map((user) => (
                  <div key={user.rank} className={`flex items-center justify-between p-4 rounded-lg ${user.username === 'You' ? 'bg-blue-500/20 border border-blue-500/30' : 'bg-gray-500/10'}`}>
                    <div className="flex items-center gap-4">
                      <div className="text-2xl font-bold text-white">#{user.rank}</div>
                      <div className="text-2xl">{user.avatar}</div>
                      <div>
                        <div className="font-semibold text-white">{user.username}</div>
                        <div className="text-gray-400 text-sm">{user.score.toLocaleString()} points</div>
                      </div>
                    </div>
                    {user.rank <= 3 && (
                      <Trophy className="w-6 h-6 text-yellow-400" />
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
