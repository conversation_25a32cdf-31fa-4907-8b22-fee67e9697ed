// Popup script for GhostLayer Chrome Extension
// Handles popup interface interactions and communication with background script

class GhostLayerPopup {
  constructor() {
    this.settings = {};
    this.stats = {};
    this.init();
  }

  async init() {
    await this.loadSettings();
    await this.loadStats();
    this.setupEventListeners();
    this.updateUI();
  }

  async loadSettings() {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ action: 'getSettings' }, (response) => {
        this.settings = response || {};
        resolve();
      });
    });
  }

  async loadStats() {
    try {
      const today = new Date().toDateString();
      const result = await chrome.storage.local.get(['dailyStats', 'totalStats']);
      
      const dailyStats = result.dailyStats || {};
      this.stats = {
        today: dailyStats[today] || 0,
        total: result.totalStats || 0
      };
    } catch (error) {
      console.error('Failed to load stats:', error);
      this.stats = { today: 0, total: 0 };
    }
  }

  setupEventListeners() {
    // Quick action buttons
    document.getElementById('humanize-selection').addEventListener('click', () => {
      this.humanizeSelection();
    });

    document.getElementById('paste-and-humanize').addEventListener('click', () => {
      this.pasteAndHumanize();
    });

    document.getElementById('open-website').addEventListener('click', () => {
      chrome.tabs.create({ url: 'https://ghostlayer.app' });
      window.close();
    });

    // Manual input
    document.getElementById('humanize-input').addEventListener('click', () => {
      this.humanizeInput();
    });

    // Settings toggles
    document.getElementById('auto-detect-toggle').addEventListener('click', () => {
      this.toggleSetting('autoDetectLanguage');
    });

    document.getElementById('notifications-toggle').addEventListener('click', () => {
      this.toggleSetting('showNotifications');
    });

    document.getElementById('analytics-toggle').addEventListener('click', () => {
      this.toggleSetting('trackUsage');
    });

    // Footer links
    document.getElementById('help-link').addEventListener('click', (e) => {
      e.preventDefault();
      chrome.tabs.create({ url: 'https://ghostlayer.app/help' });
      window.close();
    });

    document.getElementById('feedback-link').addEventListener('click', (e) => {
      e.preventDefault();
      chrome.tabs.create({ url: 'https://ghostlayer.app/feedback' });
      window.close();
    });

    document.getElementById('privacy-link').addEventListener('click', (e) => {
      e.preventDefault();
      chrome.tabs.create({ url: 'https://ghostlayer.app/privacy' });
      window.close();
    });

    // Auto-resize textarea
    const textInput = document.getElementById('text-input');
    textInput.addEventListener('input', () => {
      textInput.style.height = 'auto';
      textInput.style.height = Math.min(textInput.scrollHeight, 200) + 'px';
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'Enter':
            e.preventDefault();
            this.humanizeInput();
            break;
          case '1':
            e.preventDefault();
            this.humanizeSelection();
            break;
          case '2':
            e.preventDefault();
            this.pasteAndHumanize();
            break;
        }
      }
    });
  }

  updateUI() {
    // Update stats
    document.getElementById('today-count').textContent = this.stats.today;
    document.getElementById('total-count').textContent = this.stats.total;

    // Update settings toggles
    this.updateToggle('auto-detect-toggle', this.settings.autoDetectLanguage !== false);
    this.updateToggle('notifications-toggle', this.settings.showNotifications !== false);
    this.updateToggle('analytics-toggle', this.settings.trackUsage !== false);

    // Set default style
    const styleSelect = document.getElementById('style-select');
    styleSelect.value = this.settings.defaultStyle || 'academic';
  }

  updateToggle(toggleId, isActive) {
    const toggle = document.getElementById(toggleId);
    if (isActive) {
      toggle.classList.add('active');
    } else {
      toggle.classList.remove('active');
    }
  }

  async humanizeSelection() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const tab = tabs[0];

      chrome.tabs.sendMessage(tab.id, { action: 'getSelectedText' }, (response) => {
        if (response && response.selectedText) {
          const style = document.getElementById('style-select').value;
          this.processText(response.selectedText, style);
        } else {
          this.showMessage('Please select some text on the page first', 'warning');
        }
      });
    } catch (error) {
      this.showMessage('Failed to get selected text', 'error');
    }
  }

  async pasteAndHumanize() {
    try {
      const text = await navigator.clipboard.readText();
      if (text.trim()) {
        const style = document.getElementById('style-select').value;
        this.processText(text, style);
      } else {
        this.showMessage('Clipboard is empty', 'warning');
      }
    } catch (error) {
      this.showMessage('Could not access clipboard', 'error');
    }
  }

  humanizeInput() {
    const textInput = document.getElementById('text-input');
    const text = textInput.value.trim();
    
    if (!text) {
      this.showMessage('Please enter some text to humanize', 'warning');
      textInput.focus();
      return;
    }

    const style = document.getElementById('style-select').value;
    this.processText(text, style);
  }

  async processText(text, style) {
    const button = document.querySelector('.btn-primary');
    const originalText = button.textContent;
    
    try {
      // Show loading state
      button.textContent = '⏳ Processing...';
      button.classList.add('loading');
      
      // Hide previous result
      document.getElementById('result-preview').classList.remove('show');

      // Send to background script for processing
      chrome.runtime.sendMessage({
        action: 'humanizeText',
        text: text,
        style: style
      }, (response) => {
        if (response && response.success) {
          this.showMessage(`Text humanized successfully!`, 'success');
          // The result will be shown by the content script
          window.close();
        } else {
          this.showMessage('Failed to humanize text', 'error');
        }
      });

      // Update stats
      await this.updateStats();

    } catch (error) {
      this.showMessage('Processing failed: ' + error.message, 'error');
    } finally {
      // Restore button state
      button.textContent = originalText;
      button.classList.remove('loading');
    }
  }

  async updateStats() {
    try {
      const today = new Date().toDateString();
      const result = await chrome.storage.local.get(['dailyStats', 'totalStats']);
      
      const dailyStats = result.dailyStats || {};
      dailyStats[today] = (dailyStats[today] || 0) + 1;
      
      const totalStats = (result.totalStats || 0) + 1;
      
      await chrome.storage.local.set({
        dailyStats: dailyStats,
        totalStats: totalStats
      });

      // Update UI
      this.stats.today = dailyStats[today];
      this.stats.total = totalStats;
      this.updateUI();

    } catch (error) {
      console.error('Failed to update stats:', error);
    }
  }

  toggleSetting(settingName) {
    const currentValue = this.settings[settingName];
    const newValue = currentValue === false ? true : false;
    
    this.settings[settingName] = newValue;
    
    // Update toggle UI immediately
    const toggleId = this.getToggleId(settingName);
    this.updateToggle(toggleId, newValue);
    
    // Save to storage
    chrome.runtime.sendMessage({
      action: 'updateSettings',
      settings: { [settingName]: newValue }
    }, (response) => {
      if (response && response.success) {
        this.showMessage(`${this.getSettingDisplayName(settingName)} ${newValue ? 'enabled' : 'disabled'}`, 'info');
      }
    });
  }

  getToggleId(settingName) {
    const mapping = {
      'autoDetectLanguage': 'auto-detect-toggle',
      'showNotifications': 'notifications-toggle',
      'trackUsage': 'analytics-toggle'
    };
    return mapping[settingName];
  }

  getSettingDisplayName(settingName) {
    const mapping = {
      'autoDetectLanguage': 'Auto-detect Language',
      'showNotifications': 'Notifications',
      'trackUsage': 'Usage Analytics'
    };
    return mapping[settingName];
  }

  showMessage(message, type = 'info') {
    // Create temporary message element
    const messageEl = document.createElement('div');
    messageEl.style.cssText = `
      position: fixed;
      top: 10px;
      left: 10px;
      right: 10px;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
      z-index: 1000;
      text-align: center;
      transition: all 0.3s ease;
      transform: translateY(-100%);
    `;

    // Set colors based on type
    switch (type) {
      case 'success':
        messageEl.style.background = 'linear-gradient(45deg, #10b981, #059669)';
        messageEl.style.color = 'white';
        break;
      case 'error':
        messageEl.style.background = 'linear-gradient(45deg, #ef4444, #dc2626)';
        messageEl.style.color = 'white';
        break;
      case 'warning':
        messageEl.style.background = 'linear-gradient(45deg, #f59e0b, #d97706)';
        messageEl.style.color = 'white';
        break;
      default:
        messageEl.style.background = 'linear-gradient(45deg, #3b82f6, #2563eb)';
        messageEl.style.color = 'white';
    }

    messageEl.textContent = message;
    document.body.appendChild(messageEl);

    // Animate in
    setTimeout(() => {
      messageEl.style.transform = 'translateY(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
      messageEl.style.transform = 'translateY(-100%)';
      setTimeout(() => {
        messageEl.remove();
      }, 300);
    }, 3000);
  }

  showResult(data) {
    const resultPreview = document.getElementById('result-preview');
    const resultText = document.getElementById('result-text');
    const improvementScore = document.getElementById('improvement-score');
    const processingTime = document.getElementById('processing-time');

    resultText.textContent = data.humanizedText;
    improvementScore.textContent = `${data.improvementScore}% improvement`;
    processingTime.textContent = `${data.processingTime}ms`;

    resultPreview.classList.add('show');
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new GhostLayerPopup();
});

// Handle messages from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'showResult':
      if (window.ghostLayerPopup) {
        window.ghostLayerPopup.showResult(request.data);
      }
      break;
  }
});
