// Test the processing function directly
const fs = require('fs');
const path = require('path');

// Mock the processing function
function testProcessing() {
  console.log('=== TESTING DIRECT PROCESSING ===\n');
  
  const testText = "This is a very good example of how AI systems work with new technology.";
  
  // Simulate the enhanced processing
  function enhancedProcess(text) {
    // Step 1: Preserve structure
    const lines = text.split('\n').map(line => {
      const trimmed = line.trim();
      const indentation = line.match(/^(\s*)/)?.[1] || '';
      
      return {
        content: trimmed,
        isHeader: /^#{1,6}\s/.test(trimmed),
        isEmpty: trimmed.length === 0,
        isListItem: /^[-*+]\s/.test(trimmed) || /^\d+\.\s/.test(trimmed),
        indentation,
        originalLine: line
      };
    });
    
    // Step 2: Process only content lines
    const processedLines = lines.map(line => {
      if (line.isHeader || line.isEmpty || line.isListItem) {
        return line; // Keep unchanged
      }
      
      let content = line.content;
      
      // Protected terms
      const protectedTerms = ['AI', 'API', 'LLM', 'LLMs'];
      const protectionMap = new Map();
      
      // Protect terms
      protectedTerms.forEach((term, index) => {
        const placeholder = `PROTECTED${index}TERM`;
        const regex = new RegExp(`\\b${term}\\b`, 'g');
        content = content.replace(regex, placeholder);
        protectionMap.set(placeholder, term);
      });
      
      // Apply limited synonym replacement
      const synonyms = {
        'very': ['extremely', 'quite', 'really'],
        'good': ['excellent', 'great', 'fine'],
        'new': ['recent', 'fresh', 'modern'],
        'work': ['function', 'operate', 'perform']
      };
      
      // Apply replacements sparingly (30% chance)
      Object.entries(synonyms).forEach(([word, replacements]) => {
        if (Math.random() < 0.3) {
          const regex = new RegExp(`\\b${word}\\b`, 'gi');
          if (regex.test(content)) {
            const replacement = replacements[Math.floor(Math.random() * replacements.length)];
            content = content.replace(regex, replacement);
          }
        }
      });
      
      // Restore protected terms
      protectionMap.forEach((original, placeholder) => {
        content = content.replace(new RegExp(placeholder, 'g'), original);
      });
      
      return { ...line, content };
    });
    
    // Step 3: Reconstruct
    return processedLines.map(line => {
      if (line.isEmpty) return '';
      if (line.isHeader || line.isListItem) return line.originalLine;
      return line.indentation + line.content;
    }).join('\n');
  }

  try {
    const processed = enhancedProcess(testText);
    
    console.log('Original text:', testText);
    console.log('Processed text:', processed);
    
    // Calculate metrics
    const wordCount = testText.split(/\s+/).filter(word => word.length > 0).length;
    const sentences = testText.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const sentenceCount = sentences.length;
    const avgWordsPerSentence = wordCount / sentenceCount;
    
    console.log('\nMetrics:');
    console.log('Word count:', wordCount);
    console.log('Sentence count:', sentenceCount);
    console.log('Avg words per sentence:', avgWordsPerSentence);
    
    // Check for issues
    const hasAI = processed.includes('AI');
    const hasNoUndefined = !processed.includes('undefined');
    const hasContent = processed.length > 0;
    
    console.log('\nValidation:');
    console.log('Has AI term:', hasAI ? '✓' : '✗');
    console.log('No undefined:', hasNoUndefined ? '✓' : '✗');
    console.log('Has content:', hasContent ? '✓' : '✗');
    
    if (hasAI && hasNoUndefined && hasContent) {
      console.log('\n🎉 DIRECT PROCESSING TEST PASSED!');
    } else {
      console.log('\n❌ DIRECT PROCESSING TEST FAILED!');
    }
    
  } catch (error) {
    console.error('Direct processing failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testProcessing();
