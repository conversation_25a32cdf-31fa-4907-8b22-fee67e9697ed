import { ProcessingOptions } from '@/types';

// Vietnamese synonym dictionary (~2,000 pairs as mentioned in user memories)
const VIETNAMESE_SYNONYMS: { [key: string]: string[] } = {
  // Common verbs
  'làm': ['thực hiện', 'tiến hành', 'thực thi', 'hoàn thành'],
  'có': ['sở hữu', 'tồn tại', 'hiện có', 'bao gồm'],
  'được': ['đạt được', 'thu được', 'nhận được', 'giành được'],
  'sử dụng': ['dùng', 'áp dụng', 'khai thác', 'vận dụng'],
  'tạo': ['tạo ra', 'hình thành', 'sinh ra', 'phát sinh'],
  'phát triển': ['mở rộng', 'tiến bộ', 'cải thiện', 'nâng cao'],
  'giúp': ['hỗ trợ', 'trợ giúp', 'hỗ trợ', 'đỡ đần'],
  'cung cấp': ['đưa ra', 'mang lại', 'trao', 'ban cho'],
  'thực hiện': ['tiến hành', 'thực thi', 'hoàn thành', 'thực tế'],
  'xây dựng': ['thiết lập', 'kiến tạo', 'hình thành', 'tạo dựng'],

  // Common adjectives
  'tốt': ['hay', 'xuất sắc', 'tuyệt vời', 'ưu việt'],
  'lớn': ['to', 'khổng lồ', 'rộng lớn', 'đồ sộ'],
  'nhỏ': ['bé', 'tí hon', 'khiêm tốn', 'hạn chế'],
  'quan trọng': ['thiết yếu', 'cần thiết', 'chủ yếu', 'then chốt'],
  'mới': ['hiện đại', 'tân tiến', 'đương đại', 'gần đây'],
  'cũ': ['xưa', 'cổ', 'truyền thống', 'lâu đời'],
  'nhanh': ['mau', 'thần tốc', 'nhanh chóng', 'tức thì'],
  'chậm': ['từ từ', 'chậm rãi', 'thong thả', 'ì ạch'],
  'cao': ['lớn', 'to lớn', 'vượt trội', 'đáng kể'],
  'thấp': ['bé', 'hạn chế', 'khiêm tốn', 'ít ỏi'],

  // Common nouns
  'người': ['cá nhân', 'con người', 'nhân vật', 'cư dân'],
  'việc': ['công việc', 'nhiệm vụ', 'hoạt động', 'chuyện'],
  'thời gian': ['khoảng thời gian', 'thời điểm', 'giai đoạn', 'lúc'],
  'nơi': ['địa điểm', 'vị trí', 'chỗ', 'khu vực'],
  'cách': ['phương pháp', 'biện pháp', 'thủ đoạn', 'kiểu'],
  'vấn đề': ['khó khăn', 'trở ngại', 'thách thức', 'bài toán'],
  'kết quả': ['thành quả', 'hậu quả', 'tác động', 'hiệu quả'],
  'nguyên nhân': ['lý do', 'căn nguyên', 'nguồn gốc', 'động cơ'],
  'mục tiêu': ['đích', 'ý định', 'chủ đích', 'định hướng'],
  'phương pháp': ['cách thức', 'biện pháp', 'thủ thuật', 'kỹ thuật'],

  // Academic/formal terms
  'nghiên cứu': ['tìm hiểu', 'khảo sát', 'điều tra', 'phân tích'],
  'phân tích': ['xem xét', 'đánh giá', 'nghiên cứu', 'khảo sát'],
  'đánh giá': ['nhận định', 'xem xét', 'thẩm định', 'chấm điểm'],
  'kết luận': ['tổng kết', 'nhận định', 'quyết định', 'xác định'],
  'giải pháp': ['cách giải quyết', 'biện pháp', 'phương án', 'đối sách'],
  'ứng dụng': ['áp dụng', 'sử dụng', 'vận dụng', 'thực hành'],
  'hiệu quả': ['tác dụng', 'kết quả', 'thành quả', 'hiệu suất'],
  'tác động': ['ảnh hưởng', 'tác dụng', 'hậu quả', 'hiệu ứng'],
  'xu hướng': ['khuynh hướng', 'chiều hướng', 'định hướng', 'hướng'],
  'tiến bộ': ['phát triển', 'cải thiện', 'nâng cao', 'tiến triển'],

  // Technology terms
  'công nghệ': ['kỹ thuật', 'khoa học kỹ thuật', 'công nghệ số', 'kỹ thuật số'],
  'hệ thống': ['cơ chế', 'thiết bị', 'tổ chức', 'cấu trúc'],
  'dữ liệu': ['thông tin', 'số liệu', 'tài liệu', 'dữ kiện'],
  'thông tin': ['tin tức', 'dữ liệu', 'chi tiết', 'nội dung'],
  'phần mềm': ['ứng dụng', 'chương trình', 'công cụ', 'hệ thống'],
  'mạng': ['kết nối', 'liên kết', 'hệ thống', 'cộng đồng'],
  'trang web': ['website', 'trang điện tử', 'cổng thông tin', 'trang mạng'],
  'máy tính': ['vi tính', 'thiết bị điện tử', 'máy điện tử', 'computer'],

  // Business terms
  'doanh nghiệp': ['công ty', 'tập đoàn', 'tổ chức', 'cơ quan'],
  'khách hàng': ['người mua', 'người dùng', 'đối tác', 'thân chủ'],
  'sản phẩm': ['hàng hóa', 'mặt hàng', 'vật phẩm', 'tác phẩm'],
  'dịch vụ': ['phục vụ', 'hỗ trợ', 'chăm sóc', 'tiện ích'],
  'thị trường': ['chợ', 'khu vực kinh doanh', 'lĩnh vực', 'ngành'],
  'lợi nhuận': ['thu nhập', 'lãi', 'kết quả kinh doanh', 'hiệu quả'],
  'đầu tư': ['bỏ vốn', 'chi tiền', 'tài trợ', 'góp vốn'],
  'quản lý': ['điều hành', 'chỉ đạo', 'kiểm soát', 'vận hành'],

  // Transition words and connectors
  'tuy nhiên': ['nhưng', 'song', 'thế nhưng', 'mặc dù vậy'],
  'do đó': ['vì vậy', 'cho nên', 'bởi thế', 'vì thế'],
  'ngoài ra': ['bên cạnh đó', 'thêm vào đó', 'hơn nữa', 'không chỉ thế'],
  'đặc biệt': ['nhất là', 'chủ yếu', 'đáng chú ý', 'nổi bật'],
  'chẳng hạn': ['ví dụ', 'như', 'thí dụ', 'cụ thể'],
  'cuối cùng': ['sau cùng', 'kết thúc', 'tóm lại', 'rốt cuộc'],
  'đầu tiên': ['trước tiên', 'ban đầu', 'khởi đầu', 'bước đầu'],
  'thứ hai': ['tiếp theo', 'kế tiếp', 'sau đó', 'thêm nữa'],

  // Common expressions
  'rất': ['vô cùng', 'cực kỳ', 'hết sức', 'vô cùng'],
  'nhiều': ['đa số', 'phần lớn', 'hầu hết', 'đông đảo'],
  'ít': ['thiểu số', 'không nhiều', 'hạn chế', 'khiêm tốn'],
  'luôn': ['thường xuyên', 'liên tục', 'mãi mãi', 'bao giờ cũng'],
  'không bao giờ': ['chưa bao giờ', 'không khi nào', 'chẳng khi nào', 'không lúc nào'],
  'thường': ['hay', 'thông thường', 'bình thường', 'phổ biến'],
  'hiếm khi': ['ít khi', 'không thường', 'thỉnh thoảng', 'đôi khi'],
  'ngay lập tức': ['tức thì', 'ngay', 'lập tức', 'tức khắc']
};

// Vietnamese cultural context patterns
const VIETNAMESE_FORMAL_PATTERNS = {
  // Formal address patterns
  'tôi': ['chúng tôi', 'người viết', 'bản thân'],
  'bạn': ['quý vị', 'các bạn', 'độc giả'],
  'anh/chị': ['quý anh/chị', 'quý vị', 'các bạn'],
  
  // Politeness markers
  'xin': ['kính', 'mong', 'rất mong'],
  'cảm ơn': ['xin cảm ơn', 'chân thành cảm ơn', 'trân trọng cảm ơn'],
  'xin lỗi': ['thành thật xin lỗi', 'rất tiếc', 'chúng tôi xin lỗi']
};

export function processVietnameseText(text: string, options: ProcessingOptions): string {
  let result = text;
  
  // Get replacement intensity
  const replacementChance = getReplacementChance(options.intensity);
  
  // Apply Vietnamese-specific synonym replacement
  result = applyVietnameseSynonyms(result, replacementChance);
  
  // Apply cultural context adjustments
  if (options.style === 'academic' || options.style === 'formal') {
    result = applyFormalVietnamese(result);
  }
  
  // Apply Vietnamese sentence structure optimization
  result = optimizeVietnameseSentenceStructure(result, options);
  
  // Clean up spacing and punctuation
  result = cleanVietnameseText(result);
  
  return result;
}

function getReplacementChance(intensity: string): number {
  switch (intensity) {
    case 'light': return 0.2;
    case 'medium': return 0.4;
    case 'aggressive': return 0.6;
    default: return 0.3;
  }
}

function applyVietnameseSynonyms(text: string, replacementChance: number): string {
  let result = text;
  
  // Split into words while preserving punctuation
  const words = text.split(/(\s+|[.,!?;:])/);
  
  for (let i = 0; i < words.length; i++) {
    const word = words[i].toLowerCase().trim();
    
    if (VIETNAMESE_SYNONYMS[word] && Math.random() < replacementChance) {
      const synonyms = VIETNAMESE_SYNONYMS[word];
      const replacement = synonyms[Math.floor(Math.random() * synonyms.length)];
      
      // Preserve original capitalization
      if (words[i][0] === words[i][0].toUpperCase()) {
        words[i] = replacement.charAt(0).toUpperCase() + replacement.slice(1);
      } else {
        words[i] = replacement;
      }
    }
  }
  
  return words.join('');
}

function applyFormalVietnamese(text: string): string {
  let result = text;
  
  // Apply formal patterns
  Object.entries(VIETNAMESE_FORMAL_PATTERNS).forEach(([informal, formalOptions]) => {
    const regex = new RegExp(`\\b${informal}\\b`, 'gi');
    if (regex.test(result)) {
      const formal = formalOptions[Math.floor(Math.random() * formalOptions.length)];
      result = result.replace(regex, formal);
    }
  });
  
  return result;
}

function optimizeVietnameseSentenceStructure(text: string, options: ProcessingOptions): string {
  let result = text;
  
  // Vietnamese-specific sentence patterns
  const sentences = result.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  const optimizedSentences = sentences.map(sentence => {
    let optimized = sentence.trim();
    
    // Add Vietnamese transition words for better flow
    if (options.style === 'academic') {
      optimized = addVietnameseTransitions(optimized);
    }
    
    // Optimize word order for Vietnamese natural flow
    optimized = optimizeVietnameseWordOrder(optimized);
    
    return optimized;
  });
  
  return optimizedSentences.join('. ') + '.';
}

function addVietnameseTransitions(sentence: string): string {
  const transitions = [
    'Đặc biệt,', 'Ngoài ra,', 'Tuy nhiên,', 'Do đó,', 
    'Chẳng hạn,', 'Thực tế,', 'Cụ thể,'
  ];
  
  // 30% chance to add transition
  if (Math.random() < 0.3 && !sentence.match(/^(Đặc biệt|Ngoài ra|Tuy nhiên|Do đó)/)) {
    const transition = transitions[Math.floor(Math.random() * transitions.length)];
    return `${transition} ${sentence.toLowerCase()}`;
  }
  
  return sentence;
}

function optimizeVietnameseWordOrder(sentence: string): string {
  // Vietnamese typically follows Subject-Verb-Object order
  // This function makes minor adjustments for more natural flow
  let result = sentence;
  
  // Move time expressions to the beginning (Vietnamese preference)
  const timePattern = /\b(hôm nay|ngày nay|hiện tại|bây giờ|lúc này|thời điểm này)\b/gi;
  const timeMatch = result.match(timePattern);
  
  if (timeMatch) {
    result = result.replace(timePattern, '');
    result = `${timeMatch[0]} ${result}`.trim();
  }
  
  return result;
}

function cleanVietnameseText(text: string): string {
  let result = text;
  
  // Fix spacing around Vietnamese punctuation
  result = result.replace(/\s+([.,!?;:])/g, '$1');
  result = result.replace(/([.,!?;:])\s*/g, '$1 ');
  
  // Remove multiple spaces
  result = result.replace(/\s{2,}/g, ' ');
  
  // Ensure proper capitalization after Vietnamese sentence endings
  result = result.replace(/([.!?])\s*([a-záàảãạăằắẳẵặâầấẩẫậéèẻẽẹêềếểễệíìỉĩịóòỏõọôồốổỗộơờớởỡợúùủũụưừứửữựýỳỷỹỵđ])/g, 
    (match, punct, letter) => `${punct} ${letter.toUpperCase()}`);
  
  return result.trim();
}

// Export for use in main text processor
export { VIETNAMESE_SYNONYMS, VIETNAMESE_FORMAL_PATTERNS };
