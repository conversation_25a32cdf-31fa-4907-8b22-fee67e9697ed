export interface ProcessingOptions {
  intensity: 'light' | 'medium' | 'heavy';
  style: 'balanced' | 'formal' | 'casual' | 'academic' | 'creative' | 'technical';
  preserveFormat: boolean;
  addVariations: boolean;
}

export interface ProcessingResult {
  humanizedText: string;
  variations?: string[];
  improvementScore: number;
  detectionScore: number;
  confidence: number;
  readabilityScore: number;
  processingTime: number;
  originalLength: number;
  newLength: number;
}

export interface DetectionBreakdown {
  sentenceStructure: string;
  vocabularyVariety: string;
  flowRhythm: string;
  styleConsistency: string;
}

export interface AIDetectionResult {
  score: number;
  confidence: number;
  breakdown: DetectionBreakdown;
  suggestions: string[];
}

export interface Testimonial {
  id: string;
  name: string;
  role: string;
  company?: string;
  avatar?: string;
  rating: number;
  text: string;
  date: string;
  verified?: boolean;
}