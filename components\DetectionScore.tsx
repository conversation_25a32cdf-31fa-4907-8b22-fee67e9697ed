'use client';

import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Shield, AlertTriangle, CheckCircle, Radar } from 'lucide-react';
import { ProcessingResult } from '@/types';

interface DetectionScoreProps {
  result: ProcessingResult | null;
}

export default function DetectionScore({ result }: DetectionScoreProps) {
  if (!result) {
    return (
      <Card className="p-6 bg-white/5 backdrop-blur-lg border-white/10">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <Radar className="w-5 h-5" />
          AI Detection Analysis
        </h3>
        <div className="text-center py-8">
          <Shield className="w-12 h-12 mx-auto mb-4 text-gray-500 opacity-50" />
          <p className="text-gray-400">Process text to see detection analysis</p>
        </div>
      </Card>
    );
  }

  const getScoreColor = (score: number) => {
    if (score <= 30) return 'text-green-400';
    if (score <= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreIcon = (score: number) => {
    if (score <= 30) return <CheckCircle className="w-5 h-5 text-green-400" />;
    if (score <= 60) return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
    return <AlertTriangle className="w-5 h-5 text-red-400" />;
  };

  const getScoreDescription = (score: number) => {
    if (score <= 30) return 'Excellent - Very human-like';
    if (score <= 60) return 'Good - Mostly undetectable';
    return 'Needs improvement - Still detectable';
  };

  return (
    <Card className="p-6 bg-white/5 backdrop-blur-lg border-white/10">
      <h3 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
        <Radar className="w-5 h-5" />
        AI Detection Analysis
      </h3>

      <div className="space-y-6">
        <div>
          <div className="flex items-center justify-between mb-3">
            <span className="text-white font-medium">Overall Score</span>
            <div className="flex items-center gap-2">
              {getScoreIcon(result.detectionScore)}
              <span className={`font-bold ${getScoreColor(result.detectionScore)}`}>
                {result.detectionScore}%
              </span>
            </div>
          </div>
          
          <Progress 
            value={100 - result.detectionScore} 
            className="h-3 bg-slate-700"
          />
          
          <p className="text-sm text-gray-400 mt-2">
            {getScoreDescription(result.detectionScore)}
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="bg-slate-800/30 rounded-lg p-4">
            <div className="text-sm text-gray-400 mb-1">Confidence</div>
            <div className="text-lg font-semibold text-white">
              {result.confidence}%
            </div>
          </div>
          
          <div className="bg-slate-800/30 rounded-lg p-4">
            <div className="text-sm text-gray-400 mb-1">Readability</div>
            <div className="text-lg font-semibold text-white">
              {result.readabilityScore}/10
            </div>
          </div>
        </div>

        <div>
          <h4 className="text-white font-medium mb-3">Detection Breakdown</h4>
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Sentence Structure</span>
              <span className="text-green-400">✓ Natural</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Vocabulary Variety</span>
              <span className="text-green-400">✓ Diverse</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Flow & Rhythm</span>
              <span className="text-yellow-400">△ Good</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Style Consistency</span>
              <span className="text-green-400">✓ Consistent</span>
            </div>
          </div>
        </div>

        <div className="bg-blue-500/10 rounded-lg p-4 border border-blue-500/20">
          <div className="flex items-start gap-2">
            <Shield className="w-4 h-4 text-blue-400 mt-0.5" />
            <div>
              <div className="text-sm font-medium text-blue-400">Pro Tip</div>
              <div className="text-xs text-gray-400 mt-1">
                For better results, try adjusting the transformation intensity or writing style in the options panel.
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}