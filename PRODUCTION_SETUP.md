# 🚀 Production Setup Guide for Ghost<PERSON>ayer

## Overview
This guide covers setting up <PERSON><PERSON><PERSON><PERSON> for production with database, authentication, payment processing, and session management.

## 1. 🗄️ Production Database Setup

### PostgreSQL Database
```bash
# Install PostgreSQL (Ubuntu/Debian)
sudo apt update
sudo apt install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE ghostlayer;
CREATE USER ghostlayer_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE ghostlayer TO ghostlayer_user;
\q
```

### Database Schema
```sql
-- Users table
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHA<PERSON>(255),
  image TEXT,
  provider VARCHAR(50) DEFAULT 'credentials',
  provider_id VARCHAR(255),
  tier VARCHAR(20) DEFAULT 'free',
  credits INTEGER DEFAULT 100,
  referral_code VARCHAR(20) UNIQUE,
  referred_by INTEGER REFERENCES users(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sessions table (if using database sessions)
CREATE TABLE sessions (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  session_token VARCHAR(255) UNIQUE NOT NULL,
  expires TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Credit transactions table
CREATE TABLE credit_transactions (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL,
  type VARCHAR(20) NOT NULL, -- 'purchase', 'referral', 'usage'
  description TEXT,
  stripe_payment_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Referrals table
CREATE TABLE referrals (
  id SERIAL PRIMARY KEY,
  referrer_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  referred_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  credits_awarded INTEGER DEFAULT 50,
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'completed'
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Environment Variables
```env
# Database
DATABASE_URL=postgresql://ghostlayer_user:secure_password@localhost:5432/ghostlayer

# For Prisma (recommended ORM)
PRISMA_DATABASE_URL=postgresql://ghostlayer_user:secure_password@localhost:5432/ghostlayer
```

## 2. 🔐 Session Storage with Redis

### Redis Setup
```bash
# Install Redis (Ubuntu/Debian)
sudo apt install redis-server

# Start Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Test Redis
redis-cli ping
```

### Redis Configuration
```env
# Redis for sessions
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# Session configuration
SESSION_STORE=redis
SESSION_SECRET=your-super-secret-session-key
```

### NextAuth Redis Adapter
```bash
npm install @next-auth/redis-adapter redis
```

## 3. 💳 Payment Processing with Stripe

### Stripe Setup
1. **Create Stripe Account**: https://dashboard.stripe.com/register
2. **Get API Keys**: Dashboard → Developers → API keys
3. **Set up Webhooks**: Dashboard → Developers → Webhooks

### Stripe Environment Variables
```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_live_...  # Use sk_test_ for testing
STRIPE_PUBLISHABLE_KEY=pk_live_...  # Use pk_test_ for testing
STRIPE_WEBHOOK_SECRET=whsec_...

# Credit packages
STRIPE_PRICE_ID_100_CREDITS=price_...
STRIPE_PRICE_ID_500_CREDITS=price_...
STRIPE_PRICE_ID_1000_CREDITS=price_...
```

### Credit Packages
```javascript
// lib/stripe-config.js
export const creditPackages = [
  {
    id: 'basic',
    name: '100 Credits',
    credits: 100,
    price: 999, // $9.99 in cents
    priceId: process.env.STRIPE_PRICE_ID_100_CREDITS,
  },
  {
    id: 'popular',
    name: '500 Credits',
    credits: 500,
    price: 3999, // $39.99 in cents
    priceId: process.env.STRIPE_PRICE_ID_500_CREDITS,
    popular: true,
  },
  {
    id: 'premium',
    name: '1000 Credits',
    credits: 1000,
    price: 6999, // $69.99 in cents
    priceId: process.env.STRIPE_PRICE_ID_1000_CREDITS,
  },
];
```

## 4. 🔗 Referral System

### Referral Code Generation
```javascript
// lib/referral.js
import crypto from 'crypto';

export function generateReferralCode(userId) {
  const hash = crypto.createHash('sha256').update(`${userId}-${Date.now()}`).digest('hex');
  return hash.substring(0, 8).toUpperCase();
}

export async function processReferral(referrerCode, newUserId) {
  // Find referrer by code
  const referrer = await db.user.findUnique({
    where: { referralCode: referrerCode }
  });
  
  if (referrer) {
    // Award credits to referrer
    await db.creditTransaction.create({
      data: {
        userId: referrer.id,
        amount: 50,
        type: 'referral',
        description: `Referral bonus for inviting user ${newUserId}`
      }
    });
    
    // Update referrer's credits
    await db.user.update({
      where: { id: referrer.id },
      data: { credits: { increment: 50 } }
    });
    
    // Create referral record
    await db.referral.create({
      data: {
        referrerId: referrer.id,
        referredId: newUserId,
        creditsAwarded: 50,
        status: 'completed'
      }
    });
  }
}
```

## 5. 🏗️ Database ORM Setup (Prisma)

### Install Prisma
```bash
npm install prisma @prisma/client
npx prisma init
```

### Prisma Schema (schema.prisma)
```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            Int      @id @default(autoincrement())
  email         String   @unique
  name          String?
  image         String?
  provider      String   @default("credentials")
  providerId    String?
  tier          String   @default("free")
  credits       Int      @default(100)
  referralCode  String?  @unique
  referredBy    Int?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  sessions      Session[]
  transactions  CreditTransaction[]
  referrals     Referral[] @relation("Referrer")
  referredUsers Referral[] @relation("Referred")
  
  @@map("users")
}

model Session {
  id           Int      @id @default(autoincrement())
  userId       Int
  sessionToken String   @unique
  expires      DateTime
  createdAt    DateTime @default(now())
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
}

model CreditTransaction {
  id              Int      @id @default(autoincrement())
  userId          Int
  amount          Int
  type            String
  description     String?
  stripePaymentId String?
  createdAt       DateTime @default(now())
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("credit_transactions")
}

model Referral {
  id             Int      @id @default(autoincrement())
  referrerId     Int
  referredId     Int
  creditsAwarded Int      @default(50)
  status         String   @default("pending")
  createdAt      DateTime @default(now())
  
  referrer User @relation("Referrer", fields: [referrerId], references: [id], onDelete: Cascade)
  referred User @relation("Referred", fields: [referredId], references: [id], onDelete: Cascade)
  
  @@map("referrals")
}
```

## 6. 🔧 Updated NextAuth Configuration

### Production Auth Config
```typescript
// lib/auth.ts
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { prisma } from "@/lib/prisma";

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    // Add more providers as needed
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === "google") {
        // Handle referral code from URL
        const referralCode = // Get from session or URL
        if (referralCode) {
          await processReferral(referralCode, user.id);
        }
      }
      return true;
    },
    async session({ session, user }) {
      // Add user data to session
      const dbUser = await prisma.user.findUnique({
        where: { email: session.user.email! }
      });
      
      if (dbUser) {
        session.user.id = dbUser.id.toString();
        session.user.tier = dbUser.tier;
        session.user.credits = dbUser.credits;
        session.user.referralCode = dbUser.referralCode;
      }
      
      return session;
    },
  },
  session: {
    strategy: "database", // Use database sessions for production
  },
};
```

## 7. 🚀 Deployment Checklist

### Environment Variables for Production
```env
# App Configuration
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-super-secret-production-key

# Database
DATABASE_URL=********************************/ghostlayer

# Redis
REDIS_URL=redis://your-redis-host:6379

# Google OAuth
GOOGLE_CLIENT_ID=your-production-client-id
GOOGLE_CLIENT_SECRET=your-production-client-secret

# Stripe
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email (for notifications)
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
```

### Database Migration
```bash
# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate deploy

# Seed initial data (optional)
npx prisma db seed
```

### Build and Deploy
```bash
# Install dependencies
npm ci --production

# Build application
npm run build

# Start production server
npm start
```

## 8. 📊 Monitoring and Analytics

### Health Checks
- Database connectivity
- Redis connectivity
- Stripe webhook status
- Authentication flow

### Metrics to Track
- User registrations
- Credit purchases
- Referral conversions
- API response times
- Error rates

This setup provides a robust foundation for production deployment with user management, payment processing, and referral tracking.
