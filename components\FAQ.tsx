'use client';

import { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, HelpCircle, Search, MessageSquare } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  keywords: string[];
}

const faqData: FAQItem[] = [
  {
    id: '1',
    question: 'What is AI text humanization?',
    answer: 'AI text humanization is the process of transforming AI-generated content to make it sound more natural and human-like. GhostLayer uses advanced algorithms to modify sentence structure, vocabulary, and writing patterns while preserving the original meaning and intent of the text.',
    category: 'General',
    keywords: ['AI humanization', 'text transformation', 'natural writing']
  },
  {
    id: '2',
    question: 'How does <PERSON><PERSON>ayer detect AI-generated content?',
    answer: 'GhostLayer analyzes multiple linguistic patterns including sentence structure, vocabulary variety, flow rhythm, and style consistency. Our detection system examines repetitive patterns, unnatural phrasing, and other markers commonly found in AI-generated text to provide an accurate assessment.',
    category: 'Technology',
    keywords: ['AI detection', 'content analysis', 'linguistic patterns']
  },
  {
    id: '3',
    question: 'Is GhostLayer free to use?',
    answer: 'Yes, GhostLayer is completely free to use. You can process unlimited text without any cost, registration, or subscription. We believe in making AI humanization technology accessible to everyone, from students to professionals.',
    category: 'Pricing',
    keywords: ['free', 'pricing', 'cost', 'unlimited']
  },
  {
    id: '4',
    question: 'What writing styles does GhostLayer support?',
    answer: 'GhostLayer supports six distinct writing styles: Balanced (general purpose), Formal (business and official documents), Casual (conversational tone), Academic (scholarly writing), Creative (artistic and expressive), and Technical (specialized documentation). Each style is optimized for specific use cases.',
    category: 'Features',
    keywords: ['writing styles', 'formal', 'academic', 'creative', 'technical', 'casual']
  },
  {
    id: '5',
    question: 'How accurate is the AI detection scoring?',
    answer: 'GhostLayer\'s AI detection system provides highly accurate scoring based on multiple linguistic factors. Our algorithm analyzes sentence structure, vocabulary variety, flow patterns, and style consistency to generate confidence scores typically ranging from 85-95% accuracy.',
    category: 'Technology',
    keywords: ['accuracy', 'detection scoring', 'confidence', 'reliability']
  },
  {
    id: '6',
    question: 'Can I use GhostLayer for academic papers?',
    answer: 'Yes, GhostLayer is excellent for academic writing. The Academic style mode is specifically designed to maintain scholarly tone while improving readability. However, always ensure compliance with your institution\'s academic integrity policies and properly cite any AI assistance used in your work.',
    category: 'Academic',
    keywords: ['academic papers', 'scholarly writing', 'research', 'academic integrity']
  },
  {
    id: '7',
    question: 'Does GhostLayer store or save my text?',
    answer: 'No, GhostLayer does not store, save, or transmit your text to any servers. All processing happens locally in your browser, ensuring complete privacy and security. Your content never leaves your device, making it safe for confidential or sensitive documents.',
    category: 'Privacy',
    keywords: ['privacy', 'data security', 'local processing', 'confidential']
  },
  {
    id: '8',
    question: 'What is the maximum text length I can process?',
    answer: 'GhostLayer can handle large documents efficiently. While there\'s no strict limit, we recommend processing texts up to 10,000 words at a time for optimal performance. For longer documents, consider breaking them into smaller sections.',
    category: 'Usage',
    keywords: ['text length', 'document size', 'processing limits', 'performance']
  },
  {
    id: '9',
    question: 'How long does text processing take?',
    answer: 'Processing time depends on text length and complexity. Most documents under 1,000 words process in 0.1 seconds. Longer texts may take 1~2 seconds. The processing happens entirely in your browser, so speed also depends on your device\'s performance.',
    category: 'Performance',
    keywords: ['processing time', 'speed', 'performance', 'browser processing']
  },
  {
    id: '10',
    question: 'Can GhostLayer help bypass AI detection tools?',
    answer: 'GhostLayer is designed to improve text quality and naturalness, not to deceive detection systems. While our humanization process may reduce AI detection scores, we encourage ethical use and transparency about AI assistance in your work, especially in academic and professional contexts.',
    category: 'Ethics',
    keywords: ['AI detection bypass', 'ethical use', 'transparency', 'responsible AI']
  }
];

const categories = ['All', 'General', 'Technology', 'Features', 'Academic', 'Privacy', 'Usage', 'Performance', 'Pricing', 'Ethics'];

export default function FAQ() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  // Add structured data for FAQ
  useEffect(() => {
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": faqData.map(faq => ({
        "@type": "Question",
        "name": faq.question,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": faq.answer
        }
      }))
    };

    // Create script element for structured data
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(structuredData);
    script.id = 'faq-structured-data';

    // Remove existing script if present
    const existingScript = document.getElementById('faq-structured-data');
    if (existingScript) {
      existingScript.remove();
    }

    // Add new script to head
    document.head.appendChild(script);

    // Cleanup function
    return () => {
      const scriptToRemove = document.getElementById('faq-structured-data');
      if (scriptToRemove) {
        scriptToRemove.remove();
      }
    };
  }, []);

  const filteredFAQs = faqData.filter(faq => {
    const matchesSearch = searchTerm === '' || 
      faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <section className="py-16 px-4">
      <div className="container mx-auto max-w-4xl">

        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="absolute inset-0 bg-blue-500/20 blur-xl rounded-full"></div>
              <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-2xl">
                <HelpCircle className="w-12 h-12 text-white" />
              </div>
            </div>
          </div>
          
          <h2 className="text-4xl font-bold text-white mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            Frequently Asked Questions
          </h2>
          
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Find answers to common questions about GhostLayer's AI text humanization technology
          </p>
        </div>

        {/* Search and Filter */}
        <div className="mb-8 space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              placeholder="Search questions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white/5 border-white/10 text-white placeholder-gray-400 focus:border-blue-500"
            />
          </div>

          <div className="flex flex-wrap gap-2">
            {categories.map(category => (
              <Badge
                key={category}
                variant={selectedCategory === category ? "default" : "secondary"}
                className={`cursor-pointer transition-all ${
                  selectedCategory === category
                    ? 'bg-blue-600 hover:bg-blue-700 text-white'
                    : 'bg-white/10 hover:bg-white/20 text-gray-300'
                }`}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Badge>
            ))}
          </div>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {filteredFAQs.length === 0 ? (
            <Card className="bg-white/5 backdrop-blur-lg border-white/10 p-8 text-center">
              <CardContent className="p-0">
                <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-300">No questions found matching your search.</p>
              </CardContent>
            </Card>
          ) : (
            filteredFAQs.map((faq) => (
              <Card 
                key={faq.id}
                className="bg-white/5 backdrop-blur-lg border-white/10 hover:bg-white/10 transition-all duration-300"
              >
                <CardContent className="p-0">
                  <button
                    className="w-full p-6 text-left flex items-center justify-between"
                    onClick={() => toggleExpanded(faq.id)}
                  >
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white mb-2">
                        {faq.question}
                      </h3>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/30 text-xs">
                          {faq.category}
                        </Badge>
                      </div>
                    </div>
                    <div className="ml-4">
                      {expandedItems.includes(faq.id) ? (
                        <ChevronUp className="w-5 h-5 text-gray-400" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-400" />
                      )}
                    </div>
                  </button>
                  
                  {expandedItems.includes(faq.id) && (
                    <div className="px-6 pb-6">
                      <div className="border-t border-white/10 pt-4">
                        <p className="text-gray-300 leading-relaxed">
                          {faq.answer}
                        </p>
                        <div className="flex flex-wrap gap-1 mt-4">
                          {faq.keywords.map((keyword, index) => (
                            <Badge 
                              key={index}
                              variant="outline" 
                              className="text-xs border-gray-600 text-gray-400"
                            >
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Additional AEO Content */}
        <div className="mt-16 text-center">
          <Card className="bg-white/5 backdrop-blur-lg border-white/10 p-8">
            <CardContent className="p-0">
              <h3 className="text-2xl font-bold text-white mb-4">
                Still have questions?
              </h3>
              <p className="text-gray-300 mb-6">
                GhostLayer is designed to be intuitive and user-friendly. If you need additional help, 
                try experimenting with different settings or processing a sample text to see how it works.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
                <div>
                  <h4 className="font-semibold text-white mb-2">Quick Start</h4>
                  <p className="text-sm text-gray-400">
                    Paste your text, select a style, and click "Humanize Text" to get started.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-2">Best Practices</h4>
                  <p className="text-sm text-gray-400">
                    Use appropriate intensity levels and styles for your specific content type.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-2">Privacy First</h4>
                  <p className="text-sm text-gray-400">
                    All processing happens locally - your content never leaves your device.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
