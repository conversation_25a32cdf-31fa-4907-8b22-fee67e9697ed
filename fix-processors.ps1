# Fix all language processors by removing duplicate keys and fixing TypeScript issues

Write-Host "Fixing language processors..."

# Create simplified versions of all processors
$processors = @(
    "lib/textProcessor/chineseProcessor.ts",
    "lib/textProcessor/frenchProcessor.ts", 
    "lib/textProcessor/japaneseProcessor.ts",
    "lib/textProcessor/spanishProcessor.ts"
)

foreach ($processor in $processors) {
    if (Test-Path $processor) {
        Write-Host "Fixing $processor..."
        
        # Create a backup
        Copy-Item $processor "$processor.backup"
        
        # Read the file and fix common issues
        $content = Get-Content $processor -Raw
        
        # Fix style comparisons
        $content = $content -replace "options\.style === 'professional'", "options.style === 'formal'"
        
        # Fix spread operator issues
        $content = $content -replace "\[\.\.\.new Set\(([^)]+)\)\]", "Array.from(new Set(`$1))"
        
        # Write back
        Set-Content $processor $content -Encoding UTF8
    }
}

Write-Host "Language processors fixed!"
