import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import SecurityProvider from '@/components/SecurityProvider';
import Providers from '@/components/Providers';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'GhostLayer - AI Text Humanization Tool',
    template: '%s | GhostLayer'
  },
  description: 'Transform AI-generated content into natural, human-like writing while preserving meaning and intent. Advanced AI humanization technology for researchers, writers, and content creators.',
  keywords: [
    'AI text humanization',
    'content transformation',
    'AI detection bypass',
    'text paraphrasing',
    'AI writing tool',
    'content optimization',
    'text enhancement',
    'academic writing',
    'content creation',
    'AI content humanizer'
  ],
  authors: [{ name: 'GhostLayer Team' }],
  creator: '<PERSON><PERSON><PERSON><PERSON>',
  publisher: 'GhostLayer',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://ghostlayer.io.vn',
    title: 'GhostLayer - AI Text Humanization Tool',
    description: 'Transform AI-generated content into natural, human-like writing while preserving meaning and intent. Advanced AI humanization technology for researchers, writers, and content creators.',
    siteName: 'GhostLayer',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'GhostLayer - AI Text Humanization Tool',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'GhostLayer - AI Text Humanization Tool',
    description: 'Transform AI-generated content into natural, human-like writing while preserving meaning and intent.',
    images: ['/og-image.jpg'],
    creator: '@ghostlayer',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
};

const structuredData = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "GhostLayer",
  "description": "Transform AI-generated content into natural, human-like writing while preserving meaning and intent.",
  "url": "https://ghostlayer.io.vn",
  "applicationCategory": "BusinessApplication",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD"
  },
  "creator": {
    "@type": "Organization",
    "name": "GhostLayer Team"
  },
  "featureList": [
    "AI Text Humanization",
    "Content Transformation",
    "Multiple Writing Styles",
    "Real-time Processing",
    "Privacy-Focused"
  ],
  "screenshot": "https://ghostlayer.io.vn/screenshot.jpg"
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />

        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/logo.jpg" type="image/jpeg" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Theme color for mobile browsers */}
        <meta name="theme-color" content="#3b82f6" />
        <meta name="msapplication-TileColor" content="#3b82f6" />

        {/* PWA meta tags */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-title" content="GhostLayer" />

        {/* Viewport meta for PWA */}
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />

        {/* Google Analytics */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-LHNTYNHE5N"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-LHNTYNHE5N');
            `,
          }}
        />
      </head>
      <body className={inter.className}>
        <Providers>
          <SecurityProvider>
            {children}
          </SecurityProvider>
        </Providers>

        {/* Service Worker Registration */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);

                      // Check for updates
                      registration.addEventListener('updatefound', function() {
                        const newWorker = registration.installing;
                        newWorker.addEventListener('statechange', function() {
                          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            // New version available
                            if (confirm('New version available! Reload to update?')) {
                              newWorker.postMessage({ type: 'SKIP_WAITING' });
                              window.location.reload();
                            }
                          }
                        });
                      });
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }

              // PWA install prompt
              let deferredPrompt;
              window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                deferredPrompt = e;

                // Show install button or banner - smaller and in left corner
                const installBanner = document.createElement('div');
                installBanner.innerHTML = \`
                  <div style="position: fixed; bottom: 20px; left: 20px; width: 280px; background: linear-gradient(45deg, #3b82f6, #8b5cf6); color: white; padding: 12px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3); z-index: 1000; font-size: 14px;">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                      <div style="flex: 1;">
                        <div style="font-weight: 600; margin-bottom: 2px; font-size: 13px;">📱 Install GhostLayer</div>
                        <div style="font-size: 11px; opacity: 0.85; line-height: 1.3;">Quick access & offline use</div>
                      </div>
                      <div style="display: flex; gap: 6px; margin-left: 8px;">
                        <button onclick="installPWA()" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 11px; font-weight: 500;">Install</button>
                        <button onclick="this.parentElement.parentElement.parentElement.remove()" style="background: none; border: none; color: white; padding: 4px; cursor: pointer; font-size: 14px; line-height: 1;">✕</button>
                      </div>
                    </div>
                  </div>
                \`;
                document.body.appendChild(installBanner);
              });

              function installPWA() {
                if (deferredPrompt) {
                  deferredPrompt.prompt();
                  deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                      console.log('User accepted the install prompt');
                    }
                    deferredPrompt = null;
                  });
                }
              }

              // Handle app installed
              window.addEventListener('appinstalled', (evt) => {
                console.log('GhostLayer was installed');
                // Track installation
                if (typeof gtag !== 'undefined') {
                  gtag('event', 'pwa_installed', {
                    event_category: 'engagement',
                    event_label: 'PWA Installation'
                  });
                }
              });
            `,
          }}
        />
      </body>
    </html>
  );
}