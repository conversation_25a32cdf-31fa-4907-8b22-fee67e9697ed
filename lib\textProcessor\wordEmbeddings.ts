import { ProcessingOptions } from '@/types';

// Semantic similarity scores for word embeddings (simplified implementation)
// In a production environment, this would use actual word vectors from Word2Vec, GloVe, etc.
interface WordEmbedding {
  word: string;
  vector: number[];
  frequency: number;
  context: string[];
}

interface SemanticCluster {
  [key: string]: {
    synonyms: string[];
    semanticScore: number;
    contextualFit: string[];
    stylePreference: { [key: string]: number };
  };
}

// Semantic clusters based on contextual similarity and word embeddings
const semanticClusters: SemanticCluster = {
  // Action verbs cluster
  'implement': {
    synonyms: ['execute', 'deploy', 'apply', 'establish', 'install', 'integrate', 'operationalize'],
    semanticScore: 0.95,
    contextualFit: ['technical', 'business', 'process'],
    stylePreference: { technical: 0.9, formal: 0.8, academic: 0.7, balanced: 0.6 }
  },
  'utilize': {
    synonyms: ['employ', 'leverage', 'harness', 'exploit', 'apply', 'use', 'deploy'],
    semanticScore: 0.92,
    contextualFit: ['formal', 'academic', 'business'],
    stylePreference: { formal: 0.9, academic: 0.85, technical: 0.7, balanced: 0.6 }
  },
  'generate': {
    synonyms: ['create', 'produce', 'develop', 'construct', 'build', 'synthesize', 'formulate'],
    semanticScore: 0.88,
    contextualFit: ['technical', 'creative', 'academic'],
    stylePreference: { technical: 0.85, creative: 0.8, academic: 0.75, balanced: 0.7 }
  },
  'analyze': {
    synonyms: ['examine', 'evaluate', 'assess', 'investigate', 'scrutinize', 'study', 'review'],
    semanticScore: 0.91,
    contextualFit: ['academic', 'technical', 'formal'],
    stylePreference: { academic: 0.9, technical: 0.85, formal: 0.8, balanced: 0.7 }
  },
  'optimize': {
    synonyms: ['enhance', 'improve', 'refine', 'streamline', 'maximize', 'perfect', 'fine-tune'],
    semanticScore: 0.89,
    contextualFit: ['technical', 'business', 'academic'],
    stylePreference: { technical: 0.9, formal: 0.8, academic: 0.75, balanced: 0.7 }
  },

  // Descriptive adjectives cluster
  'significant': {
    synonyms: ['substantial', 'considerable', 'notable', 'important', 'major', 'meaningful', 'impactful'],
    semanticScore: 0.87,
    contextualFit: ['academic', 'formal', 'business'],
    stylePreference: { academic: 0.9, formal: 0.85, technical: 0.7, balanced: 0.75 }
  },
  'comprehensive': {
    synonyms: ['thorough', 'complete', 'extensive', 'detailed', 'exhaustive', 'all-encompassing', 'holistic'],
    semanticScore: 0.86,
    contextualFit: ['academic', 'formal', 'technical'],
    stylePreference: { academic: 0.9, formal: 0.85, technical: 0.8, balanced: 0.7 }
  },
  'innovative': {
    synonyms: ['groundbreaking', 'pioneering', 'cutting-edge', 'revolutionary', 'novel', 'creative', 'inventive'],
    semanticScore: 0.84,
    contextualFit: ['creative', 'business', 'technical'],
    stylePreference: { creative: 0.9, technical: 0.8, formal: 0.75, balanced: 0.8 }
  },
  'effective': {
    synonyms: ['efficient', 'successful', 'productive', 'powerful', 'impactful', 'potent', 'capable'],
    semanticScore: 0.88,
    contextualFit: ['business', 'technical', 'formal'],
    stylePreference: { formal: 0.85, technical: 0.8, academic: 0.75, balanced: 0.8 }
  },

  // Cognitive verbs cluster
  'understand': {
    synonyms: ['comprehend', 'grasp', 'perceive', 'recognize', 'realize', 'appreciate', 'discern'],
    semanticScore: 0.90,
    contextualFit: ['academic', 'casual', 'balanced'],
    stylePreference: { academic: 0.85, casual: 0.8, balanced: 0.9, formal: 0.7 }
  },
  'demonstrate': {
    synonyms: ['show', 'illustrate', 'exhibit', 'display', 'present', 'reveal', 'manifest'],
    semanticScore: 0.89,
    contextualFit: ['academic', 'formal', 'technical'],
    stylePreference: { academic: 0.9, formal: 0.85, technical: 0.8, balanced: 0.75 }
  },
  'establish': {
    synonyms: ['create', 'set up', 'found', 'institute', 'form', 'build', 'construct'],
    semanticScore: 0.87,
    contextualFit: ['formal', 'business', 'academic'],
    stylePreference: { formal: 0.9, academic: 0.85, technical: 0.8, balanced: 0.75 }
  },

  // Intensity modifiers cluster
  'extremely': {
    synonyms: ['remarkably', 'exceptionally', 'extraordinarily', 'tremendously', 'immensely', 'vastly', 'profoundly'],
    semanticScore: 0.85,
    contextualFit: ['creative', 'casual', 'formal'],
    stylePreference: { creative: 0.9, casual: 0.8, formal: 0.7, balanced: 0.75 }
  },
  'particularly': {
    synonyms: ['especially', 'notably', 'specifically', 'distinctly', 'remarkably', 'uniquely', 'singularly'],
    semanticScore: 0.88,
    contextualFit: ['academic', 'formal', 'technical'],
    stylePreference: { academic: 0.9, formal: 0.85, technical: 0.8, balanced: 0.75 }
  },

  // Connection words cluster
  'furthermore': {
    synonyms: ['additionally', 'moreover', 'besides', 'also', 'in addition', 'what is more', 'beyond that'],
    semanticScore: 0.92,
    contextualFit: ['formal', 'academic', 'technical'],
    stylePreference: { formal: 0.9, academic: 0.9, technical: 0.8, balanced: 0.7 }
  },
  'however': {
    synonyms: ['nevertheless', 'nonetheless', 'yet', 'still', 'though', 'but', 'on the other hand'],
    semanticScore: 0.90,
    contextualFit: ['formal', 'academic', 'balanced'],
    stylePreference: { formal: 0.9, academic: 0.85, balanced: 0.8, casual: 0.6 }
  }
};

// Context-aware word selection based on semantic similarity
export function getSemanticReplacement(
  word: string, 
  context: string, 
  style: ProcessingOptions['style'],
  intensity: ProcessingOptions['intensity']
): string | null {
  const lowerWord = word.toLowerCase();
  const cluster = semanticClusters[lowerWord];
  
  if (!cluster) {
    return null;
  }

  // Calculate style preference score
  const styleScore = cluster.stylePreference[style] || 0.5;
  
  // Filter synonyms based on style preference and semantic score
  const suitableSynonyms = cluster.synonyms.filter(synonym => {
    const contextFit = cluster.contextualFit.some(ctx => 
      style.includes(ctx) || ctx === 'balanced'
    );
    return contextFit && cluster.semanticScore > 0.8;
  });

  if (suitableSynonyms.length === 0) {
    return null;
  }

  // Apply intensity-based selection
  const intensityMultiplier = intensity === 'light' ? 0.3 : 
                             intensity === 'medium' ? 0.6 : 0.9;
  
  if (Math.random() > intensityMultiplier * styleScore) {
    return null;
  }

  // Select best semantic match based on context
  const bestMatch = selectBestSemanticMatch(suitableSynonyms, context, style);
  return bestMatch;
}

function selectBestSemanticMatch(
  synonyms: string[], 
  context: string, 
  style: ProcessingOptions['style']
): string {
  // Context-aware selection logic
  const contextWords = context.toLowerCase().split(/\s+/);
  
  // Score each synonym based on contextual fit
  const scoredSynonyms = synonyms.map(synonym => {
    let score = 0;
    
    // Technical context preferences
    if (style === 'technical') {
      const technicalWords = ['system', 'process', 'method', 'algorithm', 'function', 'operation'];
      if (technicalWords.some(tw => contextWords.includes(tw))) {
        if (['implement', 'execute', 'deploy', 'configure'].includes(synonym)) {
          score += 0.3;
        }
      }
    }
    
    // Academic context preferences
    if (style === 'academic') {
      const academicWords = ['research', 'study', 'analysis', 'investigation', 'evidence'];
      if (academicWords.some(aw => contextWords.includes(aw))) {
        if (['examine', 'investigate', 'analyze', 'evaluate'].includes(synonym)) {
          score += 0.3;
        }
      }
    }
    
    // Formal context preferences
    if (style === 'formal') {
      const formalWords = ['organization', 'business', 'professional', 'corporate'];
      if (formalWords.some(fw => contextWords.includes(fw))) {
        if (['establish', 'implement', 'utilize', 'demonstrate'].includes(synonym)) {
          score += 0.3;
        }
      }
    }
    
    // Creative context preferences
    if (style === 'creative') {
      const creativeWords = ['design', 'create', 'innovative', 'artistic', 'unique'];
      if (creativeWords.some(cw => contextWords.includes(cw))) {
        if (['craft', 'develop', 'generate', 'produce'].includes(synonym)) {
          score += 0.3;
        }
      }
    }
    
    // Add randomness for variety
    score += Math.random() * 0.2;
    
    return { synonym, score };
  });
  
  // Sort by score and return the best match
  scoredSynonyms.sort((a, b) => b.score - a.score);
  return scoredSynonyms[0].synonym;
}

// Enhanced semantic similarity calculation
export function calculateSemanticSimilarity(word1: string, word2: string): number {
  // Simplified semantic similarity based on word clusters
  const cluster1 = findWordCluster(word1);
  const cluster2 = findWordCluster(word2);
  
  if (cluster1 && cluster2 && cluster1 === cluster2) {
    return 0.9; // High similarity within same cluster
  }
  
  if (cluster1 && cluster2) {
    // Cross-cluster similarity based on shared contexts
    const sharedContexts = cluster1.contextualFit.filter((ctx: string) =>
      cluster2.contextualFit.includes(ctx)
    );
    return sharedContexts.length > 0 ? 0.6 : 0.3;
  }
  
  // Fallback to basic string similarity
  return calculateLevenshteinSimilarity(word1, word2);
}

function findWordCluster(word: string): any {
  const lowerWord = word.toLowerCase();
  
  // Check if word is a key in semantic clusters
  if (semanticClusters[lowerWord]) {
    return semanticClusters[lowerWord];
  }
  
  // Check if word is in any cluster's synonyms
  for (const [key, cluster] of Object.entries(semanticClusters)) {
    if (cluster.synonyms.includes(lowerWord)) {
      return cluster;
    }
  }
  
  return null;
}

function calculateLevenshteinSimilarity(word1: string, word2: string): number {
  const len1 = word1.length;
  const len2 = word2.length;
  const matrix = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(null));
  
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;
  
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = word1[i - 1] === word2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }
  
  const maxLen = Math.max(len1, len2);
  return maxLen === 0 ? 1 : (maxLen - matrix[len1][len2]) / maxLen;
}

// Context analysis for better word selection
export function analyzeContext(text: string, targetWordIndex: number): string {
  const words = text.split(/\s+/);
  const contextWindow = 5; // Words before and after target
  
  const startIndex = Math.max(0, targetWordIndex - contextWindow);
  const endIndex = Math.min(words.length, targetWordIndex + contextWindow + 1);
  
  const contextWords = words.slice(startIndex, endIndex);
  return contextWords.join(' ');
}

// Main embedding-based replacement function
export function applyEmbeddingBasedReplacement(
  text: string,
  options: ProcessingOptions
): string {
  const words = text.split(/\s+/);
  let result = text;
  
  words.forEach((word, index) => {
    // Clean word of punctuation for analysis
    const cleanWord = word.replace(/[^\w]/g, '');
    if (cleanWord.length < 3) return; // Skip short words
    
    // Get context around the word
    const context = analyzeContext(text, index);
    
    // Get semantic replacement
    const replacement = getSemanticReplacement(cleanWord, context, options.style, options.intensity);
    
    if (replacement && replacement !== cleanWord) {
      // Preserve original capitalization
      const finalReplacement = preserveCapitalization(word, replacement);
      
      // Replace in result with word boundary protection
      const wordRegex = new RegExp(`\\b${escapeRegExp(word)}\\b`, 'g');
      result = result.replace(wordRegex, finalReplacement);
    }
  });
  
  return result;
}

function preserveCapitalization(original: string, replacement: string): string {
  if (original === original.toUpperCase()) {
    return replacement.toUpperCase();
  }
  if (original[0] === original[0].toUpperCase()) {
    return replacement[0].toUpperCase() + replacement.slice(1).toLowerCase();
  }
  return replacement.toLowerCase();
}

function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
