import { ProcessingOptions } from '@/types';

// Japanese synonym dictionary (simplified version to avoid encoding issues)
const JAPANESE_SYNONYMS: { [key: string]: string[] } = {
  // Common verbs (romanized for build stability)
  'suru': ['okonau', 'jikkou', 'jisshi', 'susumeru'],
  'aru': ['sonzai', 'iru', 'ichi', 'okareru'],
  'tsukau': ['riyou', 'katsuyou', 'unyou', 'atsukau'],
  'iu': ['noberu', 'hanasu', 'hatsugen', 'hyougen'],
  'miru': ['kansatsu', 'kakunin', 'kentou', 'kanshi'],
  'omou': ['kangaeru', 'kanjiru', 'handan', 'suisoku'],
  'shiru': ['rikai', 'haaku', 'ninshiki', 'ishiki'],
  'dekiru': ['kanou', 'jitsugen', 'tassei', 'kansei'],
  'hitsuyou': ['hissu', 'fukaketsu', 'juuyou', 'motomerareru'],
  
  // Common adjectives
  'yoi': ['yuushuu', 'subarashii', 'sugureta', 'hinshitsu'],
  'ookii': ['kyodai', 'hiroi', 'koukibo', 'daikibo'],
  'chiisai': ['komaka', 'semai', 'shousai', 'konpakuto'],
  'juuyou': ['kankei', 'kakushin', 'honshitsu', 'kihon'],
  'atarashii': ['saishin', 'gendai', 'kakushin', 'shinki'],
  'hayai': ['jinsoku', 'kyuusoku', 'binkan', 'kooritsu'],
  'takai': ['kouka', 'joushou', 'koukyu', 'sentan'],
  
  // Common nouns
  'hito': ['kojin', 'jinzai', 'ningen', 'shutai'],
  'koto': ['jiken', 'dekigoto', 'mondai', 'joukyou'],
  'jikan': ['jikoku', 'kikan', 'dankai', 'toki'],
  'basho': ['ichi', 'kuiki', 'chiiki', 'genba'],
  'houhou': ['shudan', 'tetsuzuki', 'apuroochi', 'gijutsu'],
  'mondai': ['kadai', 'konnan', 'chousen', 'shougai'],
  'kekka': ['seika', 'kouka', 'eikyou', 'shuumatsu'],
  'riyuu': ['genin', 'konkyo', 'doki', 'haikei'],
  'mokuteki': ['mokuhyou', 'nerai', 'ito', 'houkou'],
  'shisutemu': ['taisei', 'kouzou', 'shikumi', 'wakugumi'],
};

export function processJapanese(text: string, options: ProcessingOptions): string {
  let result = text;
  
  // Apply synonym replacement
  result = applySynonymReplacement(result, JAPANESE_SYNONYMS);
  
  // Apply cultural context adjustments
  if (options.style === 'academic' || options.style === 'formal') {
    result = applyFormalJapanese(result);
  }
  
  return result;
}

function applySynonymReplacement(text: string, synonyms: { [key: string]: string[] }): string {
  let result = text;
  
  Object.keys(synonyms).forEach(word => {
    const alternatives = synonyms[word];
    if (alternatives.length > 0) {
      // Simple word boundary replacement
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      if (regex.test(result)) {
        const replacement = alternatives[Math.floor(Math.random() * alternatives.length)];
        result = result.replace(regex, replacement);
      }
    }
  });
  
  return result;
}

function applyFormalJapanese(text: string): string {
  // Simple formal adjustments for Japanese
  let result = text;
  
  // Add formal patterns here if needed
  result = result.replace(/\bwatashi\b/gi, 'watakushi');
  result = result.replace(/\banata\b/gi, 'anata-sama');
  
  return result;
}

// Extract key terms for analysis
export function extractJapaneseKeyTerms(text: string): string[] {
  const words = text.toLowerCase().split(/\s+/);
  const keyTerms = words.filter(word => 
    Object.keys(JAPANESE_SYNONYMS).includes(word)
  );
  
  return Array.from(new Set(keyTerms)); // Remove duplicates
}

// Detect Japanese text characteristics
export function detectJapaneseStyle(text: string): 'casual' | 'formal' | 'business' | 'academic' {
  if (text.includes('watakushi') || text.includes('anata-sama')) return 'formal';
  if (text.includes('kaisha') || text.includes('shigoto')) return 'business';
  if (text.includes('kenkyuu') || text.includes('bunseki')) return 'academic';
  
  return 'casual';
}

// Get improvement suggestions for Japanese text
export function getJapaneseImprovements(text: string): Array<{
  original: string;
  suggestion: string;
  reason: string;
}> {
  const improvements: Array<{
    original: string;
    suggestion: string;
    reason: string;
  }> = [];
  
  // Check for synonym opportunities
  Object.keys(JAPANESE_SYNONYMS).forEach(word => {
    if (text.toLowerCase().includes(word)) {
      const alternatives = JAPANESE_SYNONYMS[word];
      if (alternatives.length > 0) {
        improvements.push({
          original: word,
          suggestion: alternatives[0],
          reason: 'Synonym variation for better flow'
        });
      }
    }
  });
  
  return improvements.slice(0, 5); // Return top 5 suggestions
}
