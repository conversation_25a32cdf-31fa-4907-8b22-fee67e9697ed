# 🔐 Google OAuth Setup Guide for GhostLayer

## Overview
This guide will help you fix the "Access blocked: Authorization Error" by properly configuring Google OAuth credentials in Google Cloud Console.

## Step 1: Create Google Cloud Project

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/
   - Sign in with your Google account

2. **Create a New Project**
   - Click "Select a project" → "New Project"
   - Project name: `GhostLayer`
   - Organization: Leave as default
   - Click "Create"

## Step 2: Enable Google+ API

1. **Navigate to APIs & Services**
   - In the left sidebar, click "APIs & Services" → "Library"
   - Search for "Google+ API" or "Google Identity"
   - Click on "Google+ API" and click "Enable"

## Step 3: Configure OAuth Consent Screen

1. **Go to OAuth Consent Screen**
   - Navigate to "APIs & Services" → "OAuth consent screen"
   - Choose "External" user type (unless you have a Google Workspace)
   - Click "Create"

2. **Fill App Information**
   ```
   App name: GhostLayer
   User support email: <EMAIL>
   App logo: (optional - upload your logo)
   App domain: https://your-domain.com (or localhost for development)
   Developer contact: <EMAIL>
   ```

3. **Add Scopes**
   - Click "Add or Remove Scopes"
   - Add these scopes:
     - `../auth/userinfo.email`
     - `../auth/userinfo.profile`
     - `openid`

4. **Add Test Users** (for development)
   - Add your email and any test user emails
   - Click "Save and Continue"

## Step 4: Create OAuth Credentials

1. **Go to Credentials**
   - Navigate to "APIs & Services" → "Credentials"
   - Click "Create Credentials" → "OAuth client ID"

2. **Configure OAuth Client**
   ```
   Application type: Web application
   Name: GhostLayer Web Client
   
   Authorized JavaScript origins:
   - http://localhost:3000 (for development)
   - https://your-production-domain.com (for production)
   
   Authorized redirect URIs:
   - http://localhost:3000/api/auth/callback/google (for development)
   - https://your-production-domain.com/api/auth/callback/google (for production)
   ```

3. **Save Credentials**
   - Click "Create"
   - Copy the Client ID and Client Secret

## Step 5: Update Environment Variables

1. **Update .env.local**
   ```env
   # NextAuth Configuration
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-super-secret-jwt-secret-key-here-make-it-long-and-random
   
   # Google OAuth
   GOOGLE_CLIENT_ID=your-actual-google-client-id-from-step-4
   GOOGLE_CLIENT_SECRET=your-actual-google-client-secret-from-step-4
   ```

2. **Generate NEXTAUTH_SECRET**
   ```bash
   # Run this command to generate a secure secret
   openssl rand -base64 32
   ```

## Step 6: Production Setup

For production deployment, you'll need to:

1. **Update OAuth Redirect URIs**
   - Add your production domain to authorized origins
   - Add production callback URL: `https://yourdomain.com/api/auth/callback/google`

2. **Update Environment Variables**
   ```env
   NEXTAUTH_URL=https://your-production-domain.com
   NEXTAUTH_SECRET=your-production-secret
   GOOGLE_CLIENT_ID=same-as-development
   GOOGLE_CLIENT_SECRET=same-as-development
   ```

3. **Verify Domain Ownership**
   - In Google Cloud Console, verify your domain ownership
   - This may be required for production OAuth apps

## Step 7: Test the Setup

1. **Restart Development Server**
   ```bash
   npm run dev
   ```

2. **Test Google Sign-In**
   - Go to your app
   - Click "Sign in with Google"
   - Should redirect to Google OAuth consent screen
   - After authorization, should redirect back to your app

## Common Issues & Solutions

### "Access blocked: Authorization Error"
- **Cause**: Incorrect redirect URI or unauthorized domain
- **Solution**: Double-check redirect URIs in Google Cloud Console match exactly

### "redirect_uri_mismatch"
- **Cause**: The redirect URI doesn't match what's configured
- **Solution**: Ensure callback URL is exactly: `http://localhost:3000/api/auth/callback/google`

### "invalid_client"
- **Cause**: Wrong Client ID or Client Secret
- **Solution**: Verify credentials are copied correctly from Google Cloud Console

### OAuth Consent Screen Issues
- **Cause**: App not verified or missing required information
- **Solution**: Complete all required fields in OAuth consent screen

## Security Best Practices

1. **Keep Credentials Secret**
   - Never commit `.env.local` to version control
   - Use different credentials for development and production

2. **Restrict Domains**
   - Only add necessary domains to authorized origins
   - Remove localhost from production credentials

3. **Regular Rotation**
   - Rotate client secrets periodically
   - Monitor OAuth usage in Google Cloud Console

## Next Steps

After fixing OAuth, consider implementing:
- Database user storage
- Session management with Redis
- User roles and permissions
- Credit system integration
