import { ProcessingOptions } from '@/types';

// Chinese synonym dictionary (simplified version to avoid build issues)
const CHINESE_SYNONYMS: { [key: string]: string[] } = {
  // Simplified Chinese verbs
  '是': ['为', '乃是', '就是', '属于'],
  '有': ['拥有', '具有', '存在', '包含'],
  '做': ['进行', '实施', '执行', '完成'],
  '用': ['使用', '运用', '应用', '利用'],
  '说': ['讲', '表示', '声明', '阐述'],
  '看': ['观察', '查看', '审视', '注视'],
  '想': ['思考', '考虑', '认为', '觉得'],
  '知道': ['了解', '明白', '清楚', '懂得'],
  '可以': ['能够', '可能', '许可', '允许'],
  '需要': ['必须', '要求', '需求', '必要'],

  // Traditional Chinese verbs (unique keys)
  '為': ['乃是', '就是', '屬於', '成為'],
  '擁有': ['具有', '存在', '包含', '持有'],
  '進行': ['實施', '執行', '完成', '開展'],
  '使用': ['運用', '應用', '利用', '採用'],
  '說話': ['講', '表示', '聲明', '闡述'],
  '觀看': ['觀察', '查看', '審視', '注視'],
  '思考': ['考慮', '認為', '覺得', '想像'],
  '了解': ['明白', '清楚', '懂得', '知曉'],
  '能夠': ['可以', '可能', '許可', '允許'],
  '必須': ['需要', '要求', '需求', '必要'],

  // Common adjectives
  '好': ['优秀', '出色', '卓越', '良好'],
  '大': ['巨大', '庞大', '宽广', '广阔'],
  '小': ['微小', '细小', '狭小', '有限'],
  '重要': ['关键', '核心', '主要', '重大'],
  '新': ['最新', '现代', '当代', '崭新'],
  '快': ['迅速', '快速', '敏捷', '急速'],
  '高': ['很高', '较高', '突出', '显著'],

  // Common nouns
  '人': ['个人', '人员', '人士', '人物'],
  '事': ['事情', '事件', '工作', '任务'],
  '时间': ['时刻', '时期', '阶段', '时段'],
  '地方': ['地点', '位置', '场所', '区域'],
  '方法': ['方式', '途径', '手段', '办法'],
  '问题': ['难题', '困难', '挑战', '课题'],
  '结果': ['成果', '效果', '后果', '影响'],
  '原因': ['理由', '根源', '缘由', '动机'],
  '目标': ['目的', '宗旨', '意图', '方向'],
  '系统': ['体系', '机制', '制度', '结构'],

  // Academic terms
  '研究': ['探索', '调查', '分析', '考察'],
  '评价': ['评估', '评判', '判断', '衡量'],
  '结论': ['总结', '推论', '判断', '决定'],
  '解决': ['处理', '解答', '应对', '克服'],
  '应用': ['运用', '使用', '实践', '采用'],
  '效果': ['成效', '作用', '影响', '效应'],
  '趋势': ['倾向', '走向', '动向', '方向'],
  '发展': ['进步', '改善', '提高', '演进'],

  // Technology terms
  '技术': ['科技', '工艺', '技能', '技巧'],
  '数据': ['资料', '信息', '材料', '档案'],
  '软件': ['程序', '应用', '工具', '系统'],
  '网络': ['网路', '联网', '互联', '连接'],
  '网站': ['网页', '站点', '门户', '平台'],
  '计算机': ['电脑', '机器', '设备', '终端'],
};

// Chinese formal patterns for different contexts
const CHINESE_FORMAL_PATTERNS = {
  // Formal expressions
  '我': ['本人', '笔者', '作者'],
  '你': ['您', '阁下', '贵方'],
  '我们': ['本方', '我方', '敝方'],
  '我們': ['本方', '我方', '敝方']
};

export function processChineseText(text: string, options: ProcessingOptions, variant: 'simplified' | 'traditional' = 'simplified'): string {
  let result = text;
  
  // Get replacement intensity
  const replacementChance = getReplacementChance(options.intensity);
  
  // Apply Chinese-specific synonym replacement
  result = applyChineseSynonyms(result, replacementChance, variant);
  
  // Apply cultural context adjustments
  if (options.style === 'academic' || options.style === 'formal') {
    result = applyFormalChinese(result, variant);
  }
  
  // Apply Chinese sentence structure optimization
  result = optimizeChineseSentenceStructure(result, options);
  
  // Clean up spacing and punctuation
  result = cleanChineseText(result);
  
  return result;
}

function getReplacementChance(intensity: string): number {
  switch (intensity) {
    case 'light': return 0.2;
    case 'medium': return 0.4;
    case 'aggressive': return 0.6;
    default: return 0.3;
  }
}

function applyChineseSynonyms(text: string, replacementChance: number, variant: 'simplified' | 'traditional'): string {
  let result = text;
  
  // Chinese doesn't use spaces between words, so we need different tokenization
  const characters = text.split('');
  const words = extractChineseWords(text);
  
  words.forEach(word => {
    if (CHINESE_SYNONYMS[word] && Math.random() < replacementChance) {
      const synonyms = CHINESE_SYNONYMS[word];
      let replacement = synonyms[Math.floor(Math.random() * synonyms.length)];
      
      // Convert between simplified and traditional if needed
      if (variant === 'traditional') {
        replacement = convertToTraditional(replacement);
      } else {
        replacement = convertToSimplified(replacement);
      }
      
      // Replace in text
      const regex = new RegExp(word, 'g');
      result = result.replace(regex, replacement);
    }
  });
  
  return result;
}

function extractChineseWords(text: string): string[] {
  // Simple word extraction for Chinese - in real implementation, 
  // this would use proper Chinese word segmentation
  const words: string[] = [];
  
  // Extract 1-4 character combinations that might be words
  for (let i = 0; i < text.length; i++) {
    for (let len = 1; len <= 4 && i + len <= text.length; len++) {
      const word = text.substring(i, i + len);
      if (CHINESE_SYNONYMS[word]) {
        words.push(word);
      }
    }
  }
  
  return Array.from(new Set(words)); // Remove duplicates
}

function convertToTraditional(text: string): string {
  // Basic simplified to traditional conversion
  const conversionMap: { [key: string]: string } = {
    '为': '為', '个': '個', '时': '時', '问': '問', '结': '結',
    '评': '評', '应': '應', '发': '發', '数': '數', '软': '軟',
    '网': '網', '计': '計', '产': '產', '务': '務', '场': '場',
    '营': '營', '经': '經', '运': '運', '过': '過', '这': '這',
    '那': '那', '来': '來', '对': '對', '现': '現', '实': '實'
  };
  
  let result = text;
  Object.entries(conversionMap).forEach(([simplified, traditional]) => {
    result = result.replace(new RegExp(simplified, 'g'), traditional);
  });
  
  return result;
}

function convertToSimplified(text: string): string {
  // Basic traditional to simplified conversion
  const conversionMap: { [key: string]: string } = {
    '為': '为', '個': '个', '時': '时', '問': '问', '結': '结',
    '評': '评', '應': '应', '發': '发', '數': '数', '軟': '软',
    '網': '网', '計': '计', '產': '产', '務': '务', '場': '场',
    '營': '营', '經': '经', '運': '运', '過': '过', '這': '这',
    '來': '来', '對': '对', '現': '现', '實': '实'
  };
  
  let result = text;
  Object.entries(conversionMap).forEach(([traditional, simplified]) => {
    result = result.replace(new RegExp(traditional, 'g'), simplified);
  });
  
  return result;
}

function applyFormalChinese(text: string, variant: 'simplified' | 'traditional'): string {
  let result = text;
  
  // Apply formal patterns
  Object.entries(CHINESE_FORMAL_PATTERNS).forEach(([informal, formalOptions]) => {
    const regex = new RegExp(informal, 'g');
    if (regex.test(result)) {
      const formal = formalOptions[Math.floor(Math.random() * formalOptions.length)];
      result = result.replace(regex, formal);
    }
  });
  
  return result;
}

function optimizeChineseSentenceStructure(text: string, options: ProcessingOptions): string {
  let result = text;
  
  // Chinese sentence optimization
  const sentences = result.split(/[。！？]+/).filter(s => s.trim().length > 0);
  
  const optimizedSentences = sentences.map(sentence => {
    let optimized = sentence.trim();
    
    // Add Chinese transition words for better flow
    if (options.style === 'academic') {
      optimized = addChineseTransitions(optimized);
    }
    
    // Optimize for Chinese natural flow
    optimized = optimizeChineseWordOrder(optimized);
    
    return optimized;
  });
  
  return optimizedSentences.join('。') + '。';
}

function addChineseTransitions(sentence: string): string {
  const transitions = ['特别是', '另外', '然而', '因此', '例如', '实际上', '具体来说'];
  
  // 30% chance to add transition
  if (Math.random() < 0.3 && !sentence.match(/^(特别是|另外|然而|因此)/)) {
    const transition = transitions[Math.floor(Math.random() * transitions.length)];
    return `${transition}，${sentence}`;
  }
  
  return sentence;
}

function optimizeChineseWordOrder(sentence: string): string {
  // Chinese typically follows Subject-Verb-Object order
  // Time expressions often come at the beginning
  let result = sentence;
  
  // Move time expressions to the beginning (Chinese preference)
  const timePattern = /(今天|现在|目前|当前|此时|这时)/g;
  const timeMatch = result.match(timePattern);
  
  if (timeMatch) {
    result = result.replace(timePattern, '');
    result = `${timeMatch[0]}${result}`.trim();
  }
  
  return result;
}

function cleanChineseText(text: string): string {
  let result = text;
  
  // Fix spacing around Chinese punctuation
  result = result.replace(/\s*([，。！？；：])\s*/g, '$1');
  
  // Remove unnecessary spaces between Chinese characters
  result = result.replace(/([一-龯])\s+([一-龯])/g, '$1$2');
  
  // Ensure proper punctuation
  result = result.replace(/([。！？])\s*([一-龯])/g, '$1$2');
  
  return result.trim();
}

// Export for use in main text processor
export { CHINESE_SYNONYMS, CHINESE_FORMAL_PATTERNS };
