import { ProcessingOptions } from '@/types';

// Spanish synonym dictionary (~2,500 pairs for major global market)
const SPANISH_SYNONYMS: { [key: string]: string[] } = {
  // Common verbs
  'ser': ['estar', 'constituir', 'representar', 'significar'],
  'estar': ['encontrarse', 'hallarse', 'permanecer', 'situarse'],
  'tener': ['poseer', 'contar con', 'disponer de', 'gozar de'],
  'hacer': ['realizar', 'efectuar', 'ejecutar', 'llevar a cabo'],
  'decir': ['expresar', 'manifestar', 'declarar', 'afirmar'],
  'ver': ['observar', 'contemplar', 'visualizar', 'percibir'],
  'dar': ['otorgar', 'conceder', 'proporcionar', 'brindar'],
  'saber': ['conocer', 'dominar', 'entender', 'comprender'],
  'querer': ['desear', 'pretender', 'aspirar', 'anhelar'],
  'llegar': ['alcanzar', 'arribar', 'acceder', 'conseguir'],

  // Common adjectives
  'bueno': ['excelente', 'óptimo', 'magnífico', 'estupendo'],
  'malo': ['pésimo', 'deficiente', 'inadecuado', 'deplorable'],
  'grande': ['enorme', 'gigantesco', 'colosal', 'inmenso'],
  'pequeño': ['diminuto', 'minúsculo', 'reducido', 'limitado'],
  'importante': ['fundamental', 'esencial', 'crucial', 'relevante'],
  'nuevo': ['reciente', 'moderno', 'actual', 'contemporáneo'],
  'viejo': ['antiguo', 'añejo', 'veterano', 'tradicional'],
  'rápido': ['veloz', 'acelerado', 'ágil', 'expedito'],
  'lento': ['pausado', 'moroso', 'tardío', 'calmoso'],
  'fácil': ['sencillo', 'simple', 'elemental', 'accesible'],

  // Common nouns
  'persona': ['individuo', 'sujeto', 'ser humano', 'ciudadano'],
  'cosa': ['objeto', 'elemento', 'artículo', 'ítem'],
  'tiempo': ['período', 'época', 'momento', 'instante'],
  'lugar': ['sitio', 'ubicación', 'localización', 'emplazamiento'],
  'manera': ['forma', 'modo', 'método', 'procedimiento'],
  'problema': ['dificultad', 'inconveniente', 'obstáculo', 'contratiempo'],
  'resultado': ['consecuencia', 'efecto', 'desenlace', 'producto'],
  'razón': ['motivo', 'causa', 'fundamento', 'justificación'],
  'objetivo': ['meta', 'propósito', 'finalidad', 'intención'],
  'sistema': ['estructura', 'organización', 'mecanismo', 'esquema'],

  // Academic terms
  'investigación': ['estudio', 'análisis', 'indagación', 'exploración'],
  'análisis': ['examen', 'evaluación', 'inspección', 'revisión'],
  'evaluación': ['valoración', 'apreciación', 'estimación', 'juicio'],
  'conclusión': ['deducción', 'inferencia', 'resolución', 'determinación'],
  'solución': ['respuesta', 'resolución', 'remedio', 'alternativa'],
  'aplicación': ['uso', 'empleo', 'utilización', 'implementación'],
  'efecto': ['impacto', 'consecuencia', 'resultado', 'repercusión'],
  'influencia': ['efecto', 'impacto', 'incidencia', 'ascendiente'],
  'tendencia': ['inclinación', 'propensión', 'orientación', 'dirección'],
  'desarrollo': ['evolución', 'progreso', 'crecimiento', 'avance'],

  // Technology terms
  'tecnología': ['técnica', 'ciencia aplicada', 'innovación', 'avance técnico'],
  'plataforma': ['estructura', 'organización', 'conjunto', 'red'],
  'datos': ['información', 'estadísticas', 'cifras', 'registros'],
  'información': ['datos', 'conocimiento', 'detalles', 'contenido'],
  'programa': ['software', 'aplicación', 'herramienta', 'sistema'],
  'red': ['conexión', 'enlace', 'sistema', 'infraestructura'],
  'sitio web': ['página web', 'portal', 'plataforma digital', 'website'],
  'computadora': ['ordenador', 'equipo', 'máquina', 'dispositivo'],

  // Business terms
  'empresa': ['compañía', 'corporación', 'organización', 'entidad'],
  'cliente': ['consumidor', 'usuario', 'comprador', 'beneficiario'],
  'producto': ['artículo', 'mercancía', 'bien', 'manufactura'],
  'servicio': ['atención', 'prestación', 'asistencia', 'apoyo'],
  'mercado': ['sector', 'ámbito comercial', 'plaza', 'demanda'],
  'ganancia': ['beneficio', 'utilidad', 'provecho', 'rendimiento'],
  'inversión': ['capital', 'aportación', 'financiamiento', 'colocación'],
  'gestión': ['administración', 'manejo', 'dirección', 'control'],

  // Transition words
  'sin embargo': ['no obstante', 'pero', 'aunque', 'a pesar de'],
  'por lo tanto': ['por consiguiente', 'así pues', 'en consecuencia', 'de modo que'],
  'además': ['asimismo', 'también', 'igualmente', 'por otra parte'],
  'especialmente': ['particularmente', 'sobre todo', 'en particular', 'principalmente'],
  'por ejemplo': ['como', 'tal como', 'verbigracia', 'a modo de ejemplo'],
  'finalmente': ['por último', 'en conclusión', 'para terminar', 'al final'],
  'primero': ['en primer lugar', 'inicialmente', 'ante todo', 'para empezar'],
  'segundo': ['en segundo lugar', 'posteriormente', 'luego', 'después'],

  // Common expressions
  'muy': ['sumamente', 'extremadamente', 'enormemente', 'considerablemente'],
  'mucho': ['bastante', 'abundante', 'numeroso', 'considerable'],
  'poco': ['escaso', 'limitado', 'reducido', 'mínimo'],
  'siempre': ['constantemente', 'continuamente', 'perpetuamente', 'invariablemente'],
  'nunca': ['jamás', 'en ningún momento', 'bajo ninguna circunstancia', 'de ninguna manera'],
  'generalmente': ['habitualmente', 'comúnmente', 'frecuentemente', 'por lo general'],
  'raramente': ['pocas veces', 'esporádicamente', 'ocasionalmente', 'infrecuentemente'],
  'inmediatamente': ['al instante', 'de inmediato', 'instantáneamente', 'sin demora']
};

// Spanish formal patterns for different contexts
const SPANISH_FORMAL_PATTERNS = {
  // Formal expressions
  'yo': ['el autor', 'quien escribe', 'el suscrito'],
  'tú': ['usted', 'su persona', 'el lector'],
  'nosotros': ['los autores', 'quienes suscriben', 'el equipo'],
  
  // Politeness markers
  'por favor': ['le ruego', 'le solicito', 'tenga la bondad'],
  'gracias': ['le agradezco', 'quedo agradecido', 'expreso mi gratitud'],
  'perdón': ['disculpe', 'le pido disculpas', 'lamento']
};

// Regional variations (Latin America vs Spain)
const REGIONAL_VARIATIONS = {
  // Latin American preferences
  'computadora': ['ordenador'], // Spain uses 'ordenador'
  'auto': ['coche'], // Spain uses 'coche'
  'teléfono': ['móvil'], // Spain uses 'móvil'
  'aplicación': ['app'], // Both regions use 'app'
  
  // Formal vs informal
  'tú': ['vos'], // Some regions use 'vos'
  'ustedes': ['vosotros'] // Spain uses 'vosotros'
};

export function processSpanishText(text: string, options: ProcessingOptions, region: 'latin' | 'spain' = 'latin'): string {
  let result = text;
  
  // Get replacement intensity
  const replacementChance = getReplacementChance(options.intensity);
  
  // Apply Spanish-specific synonym replacement
  result = applySpanishSynonyms(result, replacementChance);
  
  // Apply regional variations
  result = applyRegionalVariations(result, region);
  
  // Apply cultural context adjustments
  if (options.style === 'academic' || options.style === 'formal') {
    result = applyFormalSpanish(result);
  }
  
  // Apply Spanish sentence structure optimization
  result = optimizeSpanishSentenceStructure(result, options);
  
  // Clean up spacing and punctuation
  result = cleanSpanishText(result);
  
  return result;
}

function getReplacementChance(intensity: string): number {
  switch (intensity) {
    case 'light': return 0.2;
    case 'medium': return 0.4;
    case 'aggressive': return 0.6;
    default: return 0.3;
  }
}

function applySpanishSynonyms(text: string, replacementChance: number): string {
  let result = text;
  
  // Split into words while preserving punctuation
  const words = text.split(/(\s+|[.,!?;:¿¡])/);
  
  for (let i = 0; i < words.length; i++) {
    const word = words[i].toLowerCase().trim();
    
    if (SPANISH_SYNONYMS[word] && Math.random() < replacementChance) {
      const synonyms = SPANISH_SYNONYMS[word];
      const replacement = synonyms[Math.floor(Math.random() * synonyms.length)];
      
      // Preserve original capitalization
      if (words[i][0] === words[i][0].toUpperCase()) {
        words[i] = replacement.charAt(0).toUpperCase() + replacement.slice(1);
      } else {
        words[i] = replacement;
      }
    }
  }
  
  return words.join('');
}

function applyRegionalVariations(text: string, region: 'latin' | 'spain'): string {
  let result = text;
  
  if (region === 'spain') {
    // Apply Spain-specific variations
    Object.entries(REGIONAL_VARIATIONS).forEach(([latin, spain]) => {
      const regex = new RegExp(`\\b${latin}\\b`, 'gi');
      if (regex.test(result) && Math.random() < 0.3) {
        result = result.replace(regex, spain[0]);
      }
    });
  }
  
  return result;
}

function applyFormalSpanish(text: string): string {
  let result = text;
  
  // Apply formal patterns
  Object.entries(SPANISH_FORMAL_PATTERNS).forEach(([informal, formalOptions]) => {
    const regex = new RegExp(`\\b${informal}\\b`, 'gi');
    if (regex.test(result)) {
      const formal = formalOptions[Math.floor(Math.random() * formalOptions.length)];
      result = result.replace(regex, formal);
    }
  });
  
  return result;
}

function optimizeSpanishSentenceStructure(text: string, options: ProcessingOptions): string {
  let result = text;
  
  // Spanish sentence optimization
  const sentences = result.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  const optimizedSentences = sentences.map(sentence => {
    let optimized = sentence.trim();
    
    // Add Spanish transition words for better flow
    if (options.style === 'academic') {
      optimized = addSpanishTransitions(optimized);
    }
    
    // Optimize for Spanish natural flow
    optimized = optimizeSpanishWordOrder(optimized);
    
    return optimized;
  });
  
  return optimizedSentences.join('. ') + '.';
}

function addSpanishTransitions(sentence: string): string {
  const transitions = [
    'Especialmente,', 'Además,', 'Sin embargo,', 'Por lo tanto,', 
    'Por ejemplo,', 'En efecto,', 'Concretamente,'
  ];
  
  // 30% chance to add transition
  if (Math.random() < 0.3 && !sentence.match(/^(Especialmente|Además|Sin embargo|Por lo tanto)/)) {
    const transition = transitions[Math.floor(Math.random() * transitions.length)];
    return `${transition} ${sentence.toLowerCase()}`;
  }
  
  return sentence;
}

function optimizeSpanishWordOrder(sentence: string): string {
  // Spanish typically follows Subject-Verb-Object order but is more flexible
  let result = sentence;
  
  // Move time expressions to the beginning (Spanish preference)
  const timePattern = /\b(hoy|ahora|actualmente|en la actualidad|en este momento|actualmente)\b/gi;
  const timeMatch = result.match(timePattern);
  
  if (timeMatch) {
    result = result.replace(timePattern, '');
    result = `${timeMatch[0]} ${result}`.trim();
  }
  
  return result;
}

function cleanSpanishText(text: string): string {
  let result = text;
  
  // Fix spacing around Spanish punctuation
  result = result.replace(/\s+([.,!?;:])/g, '$1');
  result = result.replace(/([.,!?;:])\s*/g, '$1 ');
  
  // Handle Spanish question and exclamation marks
  result = result.replace(/\s*¿\s*/g, '¿');
  result = result.replace(/\s*¡\s*/g, '¡');
  
  // Remove multiple spaces
  result = result.replace(/\s{2,}/g, ' ');
  
  // Ensure proper capitalization after Spanish sentence endings
  result = result.replace(/([.!?])\s*([a-záéíóúüñ])/g, 
    (match, punct, letter) => `${punct} ${letter.toUpperCase()}`);
  
  return result.trim();
}

// Export for use in main text processor
export { SPANISH_SYNONYMS, SPANISH_FORMAL_PATTERNS, REGIONAL_VARIATIONS };
