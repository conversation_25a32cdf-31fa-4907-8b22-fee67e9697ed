/**
 * Security utilities and configurations for <PERSON><PERSON><PERSON><PERSON>
 * Implements client-side security measures and validation
 */

// Security configuration constants
export const SECURITY_CONFIG = {
  // Maximum input length to prevent DoS attacks
  MAX_INPUT_LENGTH: 50000,
  
  // Rate limiting configuration
  RATE_LIMIT: {
    MAX_REQUESTS: 10,
    WINDOW_MS: 60000, // 1 minute
  },
  
  // Content validation patterns
  VALIDATION: {
    // Prevent script injection
    SCRIPT_PATTERN: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    // Prevent HTML injection
    HTML_PATTERN: /<[^>]*>/g,
    // Prevent SQL injection patterns
    SQL_PATTERN: /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
  },
};

// Rate limiting store (client-side)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

/**
 * Client-side rate limiting
 */
export function checkRateLimit(identifier: string = 'default'): boolean {
  const now = Date.now();
  const record = rateLimitStore.get(identifier);
  
  if (!record || now > record.resetTime) {
    rateLimitStore.set(identifier, {
      count: 1,
      resetTime: now + SECURITY_CONFIG.RATE_LIMIT.WINDOW_MS,
    });
    return true;
  }
  
  if (record.count >= SECURITY_CONFIG.RATE_LIMIT.MAX_REQUESTS) {
    return false;
  }
  
  record.count++;
  return true;
}

/**
 * Sanitize user input to prevent XSS and injection attacks
 */
export function sanitizeInput(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }
  
  // Check input length
  if (input.length > SECURITY_CONFIG.MAX_INPUT_LENGTH) {
    throw new Error('Input exceeds maximum allowed length');
  }
  
  // Remove potentially dangerous patterns
  let sanitized = input
    .replace(SECURITY_CONFIG.VALIDATION.SCRIPT_PATTERN, '')
    .replace(SECURITY_CONFIG.VALIDATION.HTML_PATTERN, '')
    .trim();
  
  // Additional sanitization for common attack vectors
  sanitized = sanitized
    .replace(/javascript:/gi, '')
    .replace(/vbscript:/gi, '')
    .replace(/onload=/gi, '')
    .replace(/onerror=/gi, '')
    .replace(/onclick=/gi, '');
  
  return sanitized;
}

/**
 * Validate text content for processing
 */
export function validateTextContent(text: string): { isValid: boolean; error?: string } {
  try {
    // Basic validation
    if (!text || typeof text !== 'string') {
      return { isValid: false, error: 'Invalid input: text must be a non-empty string' };
    }
    
    // Length validation
    if (text.length === 0) {
      return { isValid: false, error: 'Input cannot be empty' };
    }
    
    if (text.length > SECURITY_CONFIG.MAX_INPUT_LENGTH) {
      return { isValid: false, error: `Input exceeds maximum length of ${SECURITY_CONFIG.MAX_INPUT_LENGTH} characters` };
    }
    
    // Check for suspicious patterns
    if (SECURITY_CONFIG.VALIDATION.SCRIPT_PATTERN.test(text)) {
      return { isValid: false, error: 'Input contains potentially dangerous script content' };
    }
    
    if (SECURITY_CONFIG.VALIDATION.SQL_PATTERN.test(text)) {
      return { isValid: false, error: 'Input contains potentially dangerous SQL patterns' };
    }
    
    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'Validation failed due to unexpected error' };
  }
}

/**
 * Generate a secure random token for CSRF protection
 */
export function generateSecureToken(): string {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || typeof crypto === 'undefined') {
    // Fallback for server-side rendering
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Secure local storage operations
 */
export const secureStorage = {
  set: (key: string, value: any): void => {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return; // Skip on server-side
    }
    try {
      const sanitizedKey = sanitizeInput(key);
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(sanitizedKey, serializedValue);
    } catch (error) {
      console.warn('Failed to store data securely:', error);
    }
  },

  get: (key: string): any => {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return null; // Return null on server-side
    }
    try {
      const sanitizedKey = sanitizeInput(key);
      const item = localStorage.getItem(sanitizedKey);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.warn('Failed to retrieve data securely:', error);
      return null;
    }
  },

  remove: (key: string): void => {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return; // Skip on server-side
    }
    try {
      const sanitizedKey = sanitizeInput(key);
      localStorage.removeItem(sanitizedKey);
    } catch (error) {
      console.warn('Failed to remove data securely:', error);
    }
  },
};

/**
 * Content Security Policy violation handler
 */
export function handleCSPViolation(event: SecurityPolicyViolationEvent): void {
  console.warn('CSP Violation:', {
    blockedURI: event.blockedURI,
    violatedDirective: event.violatedDirective,
    originalPolicy: event.originalPolicy,
  });
  
  // In production, you might want to report this to your monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Report to monitoring service
    // reportSecurityViolation(event);
  }
}

/**
 * Initialize security measures
 */
export function initializeSecurity(): void {
  if (typeof window !== 'undefined') {
    // Add CSP violation event listener
    document.addEventListener('securitypolicyviolation', handleCSPViolation);
    
    // Disable right-click context menu in production (optional)
    if (process.env.NODE_ENV === 'production') {
      document.addEventListener('contextmenu', (e) => {
        e.preventDefault();
      });
      
      // Disable F12, Ctrl+Shift+I, Ctrl+U
      document.addEventListener('keydown', (e) => {
        if (
          e.key === 'F12' ||
          (e.ctrlKey && e.shiftKey && e.key === 'I') ||
          (e.ctrlKey && e.key === 'u')
        ) {
          e.preventDefault();
        }
      });
    }
  }
}

/**
 * Security headers validation (client-side check)
 */
export function validateSecurityHeaders(): void {
  if (typeof window !== 'undefined') {
    // Check if HTTPS is enforced
    if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
      console.warn('Security Warning: Application should be served over HTTPS');
    }
    
    // Check for security headers (this is informational only)
    fetch(window.location.href, { method: 'HEAD' })
      .then(response => {
        const headers = response.headers;
        
        if (!headers.get('strict-transport-security')) {
          console.warn('Security Warning: HSTS header not found');
        }
        
        if (!headers.get('x-content-type-options')) {
          console.warn('Security Warning: X-Content-Type-Options header not found');
        }
        
        if (!headers.get('x-frame-options')) {
          console.warn('Security Warning: X-Frame-Options header not found');
        }
      })
      .catch(() => {
        // Silently fail - this is just a diagnostic check
      });
  }
}
