// Test the enhanced algorithm with a sample text
const fs = require('fs');

// Sample text that represents the problematic patterns
const sampleText = `# Sample Header

## Introduction: The Power of AI

This is a very good example of how AI systems work. The new technology is important for businesses.

1. **First Point**: AI can help with many tasks
2. **Second Point**: LLMs are powerful tools
3. **Third Point**: API integration is crucial

The system will use advanced algorithms to make better results.`;

console.log('=== TESTING ENHANCED PROCESSING ===\n');
console.log('Original text:');
console.log(sampleText);
console.log('\n' + '='.repeat(50) + '\n');

// Simulate the enhanced processing
function enhancedProcess(text) {
  // Step 1: Preserve structure
  const lines = text.split('\n').map(line => {
    const trimmed = line.trim();
    const indentation = line.match(/^(\s*)/)?.[1] || '';
    
    return {
      content: trimmed,
      isHeader: /^#{1,6}\s/.test(trimmed),
      isEmpty: trimmed.length === 0,
      isListItem: /^[-*+]\s/.test(trimmed) || /^\d+\.\s/.test(trimmed),
      indentation,
      originalLine: line
    };
  });
  
  // Step 2: Process only content lines
  const processedLines = lines.map(line => {
    if (line.isHeader || line.isEmpty || line.isListItem) {
      return line; // Keep unchanged
    }
    
    let content = line.content;
    
    // Protected terms
    const protectedTerms = ['AI', 'API', 'LLM', 'LLMs'];
    const protectionMap = new Map();
    
    // Protect terms
    protectedTerms.forEach((term, index) => {
      const placeholder = `__PROTECTED_${index}__`;
      const regex = new RegExp(`\\b${term}\\b`, 'g');
      content = content.replace(regex, placeholder);
      protectionMap.set(placeholder, term);
    });
    
    // Apply limited synonym replacement
    const synonyms = {
      'very': ['extremely', 'quite', 'really'],
      'good': ['excellent', 'great', 'fine'],
      'new': ['recent', 'fresh', 'modern'],
      'important': ['crucial', 'vital', 'essential'],
      'use': ['utilize', 'employ', 'apply'],
      'make': ['create', 'produce', 'generate'],
      'better': ['improved', 'enhanced', 'superior']
    };
    
    // Apply replacements sparingly (30% chance)
    Object.entries(synonyms).forEach(([word, replacements]) => {
      if (Math.random() < 0.3) {
        const regex = new RegExp(`\\b${word}\\b`, 'gi');
        if (regex.test(content)) {
          const replacement = replacements[Math.floor(Math.random() * replacements.length)];
          content = content.replace(regex, replacement);
        }
      }
    });
    
    // Restore protected terms
    protectionMap.forEach((original, placeholder) => {
      content = content.replace(new RegExp(placeholder, 'g'), original);
    });
    
    return { ...line, content };
  });
  
  // Step 3: Reconstruct
  return processedLines.map(line => {
    if (line.isEmpty) return '';
    if (line.isHeader || line.isListItem) return line.originalLine;
    return line.indentation + line.content;
  }).join('\n');
}

// Process the sample
const processed = enhancedProcess(sampleText);

console.log('Enhanced processed text:');
console.log(processed);

console.log('\n' + '='.repeat(50) + '\n');

// Analyze the results
function analyzeResults(original, processed) {
  const analysis = [];
  
  // Check structure preservation
  const originalLines = original.split('\n');
  const processedLines = processed.split('\n');
  
  analysis.push(`Lines preserved: ${originalLines.length === processedLines.length ? 'YES' : 'NO'}`);
  
  // Check headers
  const originalHeaders = originalLines.filter(line => /^#{1,6}\s/.test(line.trim()));
  const processedHeaders = processedLines.filter(line => /^#{1,6}\s/.test(line.trim()));
  
  analysis.push(`Headers preserved: ${originalHeaders.length === processedHeaders.length ? 'YES' : 'NO'}`);
  
  // Check protected terms
  const protectedTerms = ['AI', 'API', 'LLM', 'LLMs'];
  const protectedPreserved = protectedTerms.every(term => {
    const originalCount = (original.match(new RegExp(`\\b${term}\\b`, 'g')) || []).length;
    const processedCount = (processed.match(new RegExp(`\\b${term}\\b`, 'g')) || []).length;
    return originalCount === processedCount;
  });
  
  analysis.push(`Protected terms preserved: ${protectedPreserved ? 'YES' : 'NO'}`);
  
  // Check for excessive transitions
  const excessiveTransitions = processed.match(/Furthermore, additionally|Moreover, consequently|Additionally, furthermore/gi);
  analysis.push(`Excessive transitions: ${excessiveTransitions ? excessiveTransitions.length : 0}`);
  
  return analysis;
}

const analysis = analyzeResults(sampleText, processed);

console.log('Analysis:');
analysis.forEach((item, index) => {
  console.log(`${index + 1}. ${item}`);
});

console.log('\n=== TEST COMPLETE ===');
