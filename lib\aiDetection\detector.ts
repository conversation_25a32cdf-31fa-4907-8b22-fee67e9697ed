import { AIDetectionResult, DetectionBreakdown } from '@/types';

export function analyzeAIDetection(text: string): AIDetectionResult {
  // Analyze various aspects of the text to determine AI detection likelihood
  const sentenceStructureScore = analyzeSentenceStructure(text);
  const vocabularyVarietyScore = analyzeVocabularyVariety(text);
  const flowRhythmScore = analyzeFlowRhythm(text);
  const styleConsistencyScore = analyzeStyleConsistency(text);
  
  // Calculate overall score (lower is better for avoiding detection)
  const overallScore = Math.round(
    (sentenceStructureScore + vocabularyVarietyScore + flowRhythmScore + styleConsistencyScore) / 4
  );
  
  // Calculate confidence based on text length and analysis depth
  const confidence = calculateConfidence(text, overallScore);
  
  // Generate breakdown descriptions
  const breakdown: DetectionBreakdown = {
    sentenceStructure: getScoreDescription(sentenceStructureScore, 'structure'),
    vocabularyVariety: getScoreDescription(vocabularyVarietyScore, 'vocabulary'),
    flowRhythm: getScoreDescription(flowRhythmScore, 'flow'),
    styleConsistency: getScoreDescription(styleConsistencyScore, 'style')
  };
  
  // Generate suggestions for improvement
  const suggestions = generateSuggestions(overallScore, breakdown);
  
  return {
    score: overallScore,
    confidence,
    breakdown,
    suggestions
  };
}

function analyzeSentenceStructure(text: string): number {
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  if (sentences.length === 0) return 100;
  
  let structureScore = 0;
  let penalties = 0;
  
  // Analyze sentence length variation
  const lengths = sentences.map(s => s.trim().split(/\s+/).length);
  const avgLength = lengths.reduce((a, b) => a + b, 0) / lengths.length;
  const lengthVariation = calculateVariation(lengths);
  
  // Penalty for too uniform sentence lengths (AI characteristic)
  if (lengthVariation < 0.3) {
    penalties += 20;
  }
  
  // Penalty for too many very long sentences
  const longSentences = lengths.filter(l => l > 30).length;
  if (longSentences / sentences.length > 0.3) {
    penalties += 15;
  }
  
  // Penalty for too many very short sentences
  const shortSentences = lengths.filter(l => l < 8).length;
  if (shortSentences / sentences.length > 0.4) {
    penalties += 10;
  }
  
  // Analyze sentence starters
  const starters = sentences.map(s => s.trim().split(/\s+/)[0].toLowerCase());
  const starterVariety = new Set(starters).size / starters.length;
  
  // Penalty for repetitive sentence starters
  if (starterVariety < 0.7) {
    penalties += 15;
  }
  
  // Analyze complex vs simple sentences
  const complexSentences = sentences.filter(s => 
    s.includes(',') || s.includes(';') || s.includes(':') || 
    /\b(which|that|where|when|while|although|because|since|unless|until)\b/i.test(s)
  ).length;
  
  const complexRatio = complexSentences / sentences.length;
  
  // Penalty for too few complex sentences (AI tends to be simpler)
  if (complexRatio < 0.3) {
    penalties += 10;
  }
  
  // Penalty for too many complex sentences (overly complex AI)
  if (complexRatio > 0.8) {
    penalties += 15;
  }
  
  structureScore = Math.min(100, penalties);
  return structureScore;
}

function analyzeVocabularyVariety(text: string): number {
  const words = text.toLowerCase().match(/\b\w+\b/g) || [];
  
  if (words.length === 0) return 100;
  
  let vocabularyScore = 0;
  let penalties = 0;
  
  // Calculate lexical diversity (unique words / total words)
  const uniqueWords = new Set(words);
  const lexicalDiversity = uniqueWords.size / words.length;
  
  // Penalty for low lexical diversity (AI characteristic)
  if (lexicalDiversity < 0.4) {
    penalties += 25;
  }
  
  // Analyze word frequency distribution
  const wordFreq: { [key: string]: number } = {};
  words.forEach(word => {
    wordFreq[word] = (wordFreq[word] || 0) + 1;
  });
  
  // Check for overused words
  const overusedWords = Object.entries(wordFreq)
    .filter(([word, freq]) => freq > 3 && word.length > 3)
    .filter(([word]) => !isCommonWord(word));
  
  if (overusedWords.length > words.length * 0.02) {
    penalties += 15;
  }
  
  // Analyze vocabulary sophistication
  const sophisticatedWords = words.filter(word => 
    word.length > 6 && !isCommonWord(word)
  ).length;
  
  const sophisticationRatio = sophisticatedWords / words.length;
  
  // Penalty for too low sophistication
  if (sophisticationRatio < 0.1) {
    penalties += 10;
  }
  
  // Penalty for too high sophistication (overly complex AI)
  if (sophisticationRatio > 0.4) {
    penalties += 20;
  }
  
  // Check for AI-typical word patterns
  const aiWords = [
    'furthermore', 'moreover', 'additionally', 'consequently', 'nevertheless',
    'utilize', 'implement', 'facilitate', 'optimize', 'enhance', 'comprehensive',
    'significant', 'substantial', 'considerable', 'various', 'numerous'
  ];
  
  const aiWordCount = words.filter(word => aiWords.includes(word)).length;
  if (aiWordCount > words.length * 0.03) {
    penalties += 20;
  }
  
  vocabularyScore = Math.min(100, penalties);
  return vocabularyScore;
}

function analyzeFlowRhythm(text: string): number {
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  if (sentences.length === 0) return 100;
  
  let flowScore = 0;
  let penalties = 0;
  
  // Analyze transition usage
  const transitions = [
    'however', 'therefore', 'furthermore', 'moreover', 'additionally',
    'consequently', 'nevertheless', 'nonetheless', 'meanwhile', 'subsequently'
  ];
  
  let transitionCount = 0;
  sentences.forEach(sentence => {
    const lowerSentence = sentence.toLowerCase();
    transitions.forEach(transition => {
      if (lowerSentence.includes(transition)) {
        transitionCount++;
      }
    });
  });
  
  const transitionRatio = transitionCount / sentences.length;
  
  // Penalty for too many formal transitions (AI characteristic)
  if (transitionRatio > 0.3) {
    penalties += 25;
  }
  
  // Penalty for no transitions (choppy flow)
  if (transitionRatio === 0 && sentences.length > 3) {
    penalties += 10;
  }
  
  // Analyze sentence rhythm patterns
  const syllableCounts = sentences.map(s => estimateSyllables(s));
  const rhythmVariation = calculateVariation(syllableCounts);
  
  // Penalty for too uniform rhythm
  if (rhythmVariation < 0.4) {
    penalties += 15;
  }
  
  // Analyze paragraph structure (if line breaks are preserved)
  const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
  if (paragraphs.length > 1) {
    const paragraphLengths = paragraphs.map(p => p.split(/[.!?]+/).length);
    const paragraphVariation = calculateVariation(paragraphLengths);
    
    // Penalty for uniform paragraph lengths
    if (paragraphVariation < 0.3) {
      penalties += 10;
    }
  }
  
  flowScore = Math.min(100, penalties);
  return flowScore;
}

function analyzeStyleConsistency(text: string): number {
  let styleScore = 0;
  let penalties = 0;
  
  // Analyze formality consistency
  const formalWords = [
    'utilize', 'implement', 'facilitate', 'demonstrate', 'acquire',
    'commence', 'conclude', 'subsequently', 'furthermore', 'moreover'
  ];
  
  const casualWords = [
    'get', 'make', 'do', 'go', 'come', 'put', 'take', 'give',
    'also', 'but', 'so', 'really', 'very', 'pretty', 'quite'
  ];
  
  const words = text.toLowerCase().match(/\b\w+\b/g) || [];
  const formalCount = words.filter(word => formalWords.includes(word)).length;
  const casualCount = words.filter(word => casualWords.includes(word)).length;
  
  const formalRatio = formalCount / words.length;
  const casualRatio = casualCount / words.length;
  
  // Penalty for inconsistent formality (mixing too much)
  if (formalRatio > 0.02 && casualRatio > 0.1) {
    penalties += 15;
  }
  
  // Analyze punctuation patterns
  const exclamations = (text.match(/!/g) || []).length;
  const questions = (text.match(/\?/g) || []).length;
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
  
  const exclamationRatio = exclamations / sentences;
  const questionRatio = questions / sentences;
  
  // Penalty for inconsistent punctuation style
  if (exclamationRatio > 0.1 && questionRatio > 0.1) {
    penalties += 10;
  }
  
  // Analyze contraction usage
  const contractions = (text.match(/\b\w+'\w+\b/g) || []).length;
  const contractionRatio = contractions / words.length;
  
  // Check for mixed contraction usage (inconsistent style)
  const expandedForms = ['cannot', 'will not', 'do not', 'is not', 'are not'];
  const expandedCount = words.filter(word => expandedForms.includes(word)).length;
  
  if (contractionRatio > 0.01 && expandedCount > 0) {
    penalties += 10;
  }
  
  styleScore = Math.min(100, penalties);
  return styleScore;
}

function calculateVariation(numbers: number[]): number {
  if (numbers.length === 0) return 0;
  
  const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
  const variance = numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
  const standardDeviation = Math.sqrt(variance);
  
  return standardDeviation / mean; // Coefficient of variation
}

function estimateSyllables(text: string): number {
  // Simple syllable estimation
  const words = text.match(/\b\w+\b/g) || [];
  return words.reduce((total, word) => {
    const syllables = word.toLowerCase()
      .replace(/[^aeiouy]/g, '')
      .replace(/[aeiouy]+/g, 'a')
      .length;
    return total + Math.max(1, syllables);
  }, 0);
}

function isCommonWord(word: string): boolean {
  const commonWords = new Set([
    'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have',
    'i', 'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you',
    'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they',
    'we', 'say', 'her', 'she', 'or', 'an', 'will', 'my',
    'one', 'all', 'would', 'there', 'their', 'what', 'so',
    'up', 'out', 'if', 'about', 'who', 'get', 'which', 'go',
    'me', 'when', 'make', 'can', 'like', 'time', 'no', 'just',
    'him', 'know', 'take', 'people', 'into', 'year', 'your',
    'good', 'some', 'could', 'them', 'see', 'other', 'than',
    'then', 'now', 'look', 'only', 'come', 'its', 'over',
    'think', 'also', 'back', 'after', 'use', 'two', 'how',
    'our', 'work', 'first', 'well', 'way', 'even', 'new',
    'want', 'because', 'any', 'these', 'give', 'day', 'most',
    'us', 'is', 'was', 'are', 'been', 'has', 'had', 'were'
  ]);
  
  return commonWords.has(word.toLowerCase());
}

function calculateConfidence(text: string, score: number): number {
  const words = text.match(/\b\w+\b/g) || [];
  const wordCount = words.length;
  
  let confidence = 85; // Base confidence
  
  // Adjust based on text length
  if (wordCount < 50) {
    confidence -= 15; // Less reliable for short texts
  } else if (wordCount > 500) {
    confidence += 10; // More reliable for longer texts
  }
  
  // Adjust based on score certainty
  if (score < 20 || score > 80) {
    confidence += 5; // More confident in extreme scores
  }
  
  return Math.min(99, Math.max(60, confidence));
}

function getScoreDescription(score: number, category: string): string {
  const descriptions = {
    structure: {
      excellent: 'Natural variation in sentence length and complexity',
      good: 'Adequate sentence structure with minor patterns',
      fair: 'Some repetitive patterns in sentence construction',
      poor: 'Highly uniform or unnatural sentence structures'
    },
    vocabulary: {
      excellent: 'Rich, varied vocabulary with natural word choice',
      good: 'Good vocabulary variety with occasional repetition',
      fair: 'Limited vocabulary variety, some overused terms',
      poor: 'Repetitive vocabulary with AI-typical word patterns'
    },
    flow: {
      excellent: 'Natural rhythm and smooth transitions',
      good: 'Generally good flow with minor rhythm issues',
      fair: 'Adequate flow but some choppy or forced transitions',
      poor: 'Poor flow with excessive formal transitions'
    },
    style: {
      excellent: 'Consistent, natural writing style throughout',
      good: 'Mostly consistent style with minor variations',
      fair: 'Some inconsistencies in formality or tone',
      poor: 'Inconsistent style mixing formal and casual elements'
    }
  };
  
  const categoryDescriptions = descriptions[category as keyof typeof descriptions];
  
  if (score <= 20) return categoryDescriptions.excellent;
  if (score <= 40) return categoryDescriptions.good;
  if (score <= 60) return categoryDescriptions.fair;
  return categoryDescriptions.poor;
}

function generateSuggestions(score: number, breakdown: DetectionBreakdown): string[] {
  const suggestions: string[] = [];
  
  if (score > 60) {
    suggestions.push('Consider using higher transformation intensity for better humanization');
  }
  
  if (breakdown.sentenceStructure.includes('uniform') || breakdown.sentenceStructure.includes('repetitive')) {
    suggestions.push('Vary sentence lengths and structures more naturally');
    suggestions.push('Mix simple and complex sentences for better flow');
  }
  
  if (breakdown.vocabularyVariety.includes('repetitive') || breakdown.vocabularyVariety.includes('overused')) {
    suggestions.push('Use more diverse vocabulary and avoid repetitive terms');
    suggestions.push('Replace AI-typical words with more natural alternatives');
  }
  
  if (breakdown.flowRhythm.includes('choppy') || breakdown.flowRhythm.includes('excessive')) {
    suggestions.push('Improve text flow with more natural transitions');
    suggestions.push('Reduce formal transitional phrases for casual content');
  }
  
  if (breakdown.styleConsistency.includes('inconsistent') || breakdown.styleConsistency.includes('mixing')) {
    suggestions.push('Maintain consistent formality level throughout the text');
    suggestions.push('Choose either formal or casual style and stick to it');
  }
  
  if (suggestions.length === 0) {
    suggestions.push('Text appears naturally human-like with good variation');
    suggestions.push('Consider minor adjustments to further improve naturalness');
  }
  
  return suggestions;
}